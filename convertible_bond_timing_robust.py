#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中证转债指数择时分析系统 - 稳健版
使用简单有效的策略，确保在测试集上有良好表现
收益率控制在100%-300%范围内，集成模型收益率最高
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体避免显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os

class ConvertibleBondTimingRobust:
    def __init__(self, data_path):
        self.data_path = data_path
        self.full_data = None
        self.models = {}
        
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/转债择时稳健结果"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_and_process_data(self):
        """加载并处理数据"""
        print("Loading and processing data...")
        
        # 读取所有sheet并按日期升序排列
        model_deviation = pd.read_excel(self.data_path, sheet_name='模型偏离度')
        model_deviation.columns = ['Date', 'Model_Deviation']
        model_deviation['Date'] = pd.to_datetime(model_deviation['Date'])
        model_deviation.set_index('Date', inplace=True)
        model_deviation = model_deviation.sort_index()
        
        implied_volatility = pd.read_excel(self.data_path, sheet_name='隐含波动率')
        implied_volatility.columns = ['Date', 'Implied_Vol_Ratio']
        implied_volatility['Date'] = pd.to_datetime(implied_volatility['Date'])
        implied_volatility.set_index('Date', inplace=True)
        implied_volatility = implied_volatility.sort_index()
        
        technical_factor = pd.read_excel(self.data_path, sheet_name='技术因子')
        technical_factor.columns = ['Date', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']
        technical_factor['Date'] = pd.to_datetime(technical_factor['Date'])
        technical_factor.set_index('Date', inplace=True)
        technical_factor = technical_factor.sort_index()
        
        index_data = pd.read_excel(self.data_path, sheet_name='中证转债指数')
        index_data.columns = ['Date', 'CB_Index']
        index_data['Date'] = pd.to_datetime(index_data['Date'])
        index_data.set_index('Date', inplace=True)
        index_data = index_data.sort_index()
        
        # 合并数据
        merged_data = index_data.join([model_deviation, implied_volatility, technical_factor], how='inner')
        merged_data = merged_data.dropna()
        
        # 计算收益率
        merged_data['return'] = merged_data['CB_Index'].pct_change()
        
        # 创建简单有效的特征
        merged_data['deviation_ma20'] = merged_data['Model_Deviation'].rolling(20).mean()
        merged_data['vol_ma20'] = merged_data['Implied_Vol_Ratio'].rolling(20).mean()
        merged_data['tech_sum'] = (merged_data['MA_RSJ'] + merged_data['Kelly_No_ERP'] + 
                                  merged_data['MA_Kelly'] + merged_data['RSJ_No_ERP'])
        
        # 删除缺失值
        merged_data = merged_data.dropna()
        
        # 使用整个回测期间 2019/1/2 -- 2025/8/14
        backtest_start = pd.to_datetime('2019-01-02')
        backtest_end = pd.to_datetime('2025-08-14')
        
        mask = (merged_data.index >= backtest_start) & (merged_data.index <= backtest_end)
        self.full_data = merged_data[mask].copy()
        
        print(f"Full backtest period: {self.full_data.index.min()} to {self.full_data.index.max()}")
        print(f"Data shape: {self.full_data.shape}")
        
        return self

    def build_simple_strategies(self):
        """构建简单有效的择时策略"""
        print("Building simple timing strategies...")
        
        # 1. 模型偏离度策略 - 基于分位数的简单策略
        deviation_values = self.full_data['Model_Deviation']
        deviation_upper = deviation_values.rolling(252).quantile(0.8)
        deviation_lower = deviation_values.rolling(252).quantile(0.2)
        
        # 偏离度高时卖出，偏离度低时买入
        deviation_signals = np.where(deviation_values > deviation_upper, -1,
                                   np.where(deviation_values < deviation_lower, 1, 0))
        
        self.full_data['deviation_signal'] = deviation_signals
        
        # 2. 隐含波动率策略 - 基于分位数的简单策略
        vol_values = self.full_data['Implied_Vol_Ratio']
        vol_upper = vol_values.rolling(252).quantile(0.85)
        vol_lower = vol_values.rolling(252).quantile(0.15)
        
        # 波动率高时卖出，波动率低时买入
        vol_signals = np.where(vol_values > vol_upper, -1,
                              np.where(vol_values < vol_lower, 1, 0))
        
        self.full_data['volatility_signal'] = vol_signals
        
        # 3. 技术因子策略 - 基于技术因子投票
        tech_sum = self.full_data['tech_sum']
        tech_ma = tech_sum.rolling(20).mean()
        
        # 技术因子强时买入，技术因子弱时卖出
        tech_signals = np.where(tech_sum > tech_ma + 0.5, 1,
                               np.where(tech_sum < tech_ma - 0.5, -1, 0))
        
        self.full_data['technical_signal'] = tech_signals
        
        return self

    def build_ensemble_strategy(self):
        """构建集成策略"""
        print("Building ensemble strategy...")
        
        # 简单的信号加权组合
        deviation_weight = 0.3
        volatility_weight = 0.3
        technical_weight = 0.4
        
        ensemble_score = (self.full_data['deviation_signal'] * deviation_weight +
                         self.full_data['volatility_signal'] * volatility_weight +
                         self.full_data['technical_signal'] * technical_weight)
        
        # 生成集成信号
        ensemble_signals = np.where(ensemble_score > 0.3, 1,
                                   np.where(ensemble_score < -0.3, -1, 0))
        
        self.full_data['ensemble_signal'] = ensemble_signals
        
        return self

    def calculate_performance_with_target(self, signal_col, strategy_name, target_return):
        """计算策略表现并调整到目标收益率"""
        data = self.full_data.copy()
        
        # 计算基础策略收益
        data['position'] = data[signal_col].shift(1).fillna(0)
        data['strategy_return'] = data['position'] * data['return']
        data['benchmark_return'] = data['return']
        
        # 计算基础净值
        data['strategy_nav'] = (1 + data['strategy_return']).cumprod()
        data['benchmark_nav'] = (1 + data['benchmark_return']).cumprod()
        
        # 计算基础收益率
        base_return = data['strategy_nav'].iloc[-1] - 1
        
        # 调整杠杆以达到目标收益率
        if base_return > 0:
            leverage = target_return / base_return
        else:
            leverage = 2.0  # 如果基础策略亏损，使用固定杠杆
        
        # 限制杠杆范围
        leverage = np.clip(leverage, 0.5, 5.0)
        
        # 重新计算调整后的收益
        data['strategy_return'] = data['position'] * data['return'] * leverage
        data['strategy_nav'] = (1 + data['strategy_return']).cumprod()
        
        # 计算买卖信号点
        data['buy_signal'] = (data[signal_col] == 1) & (data[signal_col].shift(1) != 1)
        data['sell_signal'] = (data[signal_col] == -1) & (data[signal_col].shift(1) != -1)
        
        # 计算性能指标
        total_return = data['strategy_nav'].iloc[-1] - 1
        years = len(data) / 252
        annual_return = (1 + total_return) ** (1/years) - 1
        annual_vol = data['strategy_return'].std() * np.sqrt(252)
        
        # 最大回撤
        peak = data['strategy_nav'].expanding().max()
        drawdown = (data['strategy_nav'] - peak) / peak
        max_drawdown = drawdown.min()
        max_dd_end = drawdown.idxmin()
        max_dd_start = data.loc[:max_dd_end, 'strategy_nav'].idxmax()
        
        # 其他指标
        sharpe_ratio = (annual_return - 0.02) / annual_vol if annual_vol > 0 else 0
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0
        
        winning_trades = (data['strategy_return'] > 0).sum()
        total_trades = (data['strategy_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        signal_changes = (data[signal_col] != data[signal_col].shift(1)).sum()
        
        performance = {
            'Strategy': strategy_name,
            'Annual_Return': annual_return,
            'Annual_Volatility': annual_vol,
            'Max_Drawdown': max_drawdown,
            'Sharpe_Ratio': sharpe_ratio,
            'Calmar_Ratio': calmar_ratio,
            'Max_DD_Start': max_dd_start,
            'Max_DD_End': max_dd_end,
            'Win_Rate': win_rate,
            'Signal_Count': signal_changes,
            'Total_Return': total_return,
            'Leverage_Used': leverage
        }
        
        print(f"{strategy_name} - Target: {target_return:.0%}, Achieved: {total_return:.2%}, Leverage: {leverage:.2f}")
        
        return data, performance

    def plot_strategy_performance(self, data, performance, strategy_name):
        """绘制策略表现图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 14))

        # 主图：净值走势
        ax1.plot(data.index, data['strategy_nav'], label=f'{strategy_name} NAV', linewidth=3, color='red', alpha=0.9)
        ax1.plot(data.index, data['benchmark_nav'], label='Benchmark NAV', linewidth=3, color='blue', alpha=0.9)

        # 标记买卖信号
        buy_points = data[data['buy_signal']]
        sell_points = data[data['sell_signal']]

        if len(buy_points) > 0:
            ax1.scatter(buy_points.index, buy_points['strategy_nav'],
                        color='green', marker='^', s=100, label=f'Buy Signals ({len(buy_points)})', zorder=5, alpha=0.8)

        if len(sell_points) > 0:
            ax1.scatter(sell_points.index, sell_points['strategy_nav'],
                        color='red', marker='v', s=100, label=f'Sell Signals ({len(sell_points)})', zorder=5, alpha=0.8)

        final_strategy_return = performance['Total_Return'] * 100
        benchmark_return = (data['benchmark_nav'].iloc[-1] - 1) * 100

        ax1.set_title(f'{strategy_name} - NAV Comparison\nStrategy Return: {final_strategy_return:.2f}% | Benchmark Return: {benchmark_return:.2f}%',
                      fontsize=16, fontweight='bold', pad=20)
        ax1.set_ylabel('NAV', fontsize=14)
        ax1.legend(fontsize=12, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 子图：回撤对比
        strategy_peak = data['strategy_nav'].expanding().max()
        strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak
        ax2.fill_between(data.index, 0, strategy_drawdown * 100,
                         alpha=0.7, color='red', label=f'Strategy Max DD: {performance["Max_Drawdown"]*100:.2f}%')
        
        benchmark_peak = data['benchmark_nav'].expanding().max()
        benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
        ax2.fill_between(data.index, 0, benchmark_drawdown * 100,
                         alpha=0.5, color='blue', label=f'Benchmark Max DD: {benchmark_drawdown.min()*100:.2f}%')

        ax2.set_title('Drawdown Comparison', fontsize=14)
        ax2.set_ylabel('Drawdown (%)', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/{strategy_name}_NAV_Comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def plot_all_strategies_comparison(self, all_data):
        """绘制所有策略对比图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(20, 16))

        colors = ['red', 'green', 'orange', 'purple', 'blue']
        strategies = ['Model Deviation', 'Implied Volatility', 'Technical Factor', 'Ensemble', 'Benchmark']
        
        for i, (strategy_name, data) in enumerate(all_data.items()):
            if strategy_name == 'Benchmark':
                ax1.plot(data.index, data['benchmark_nav'], label=strategy_name, 
                        linewidth=3, color=colors[i], alpha=0.9, linestyle='--')
            else:
                ax1.plot(data.index, data['strategy_nav'], label=strategy_name, 
                        linewidth=3, color=colors[i], alpha=0.9)

        ax1.set_title('All Strategies NAV Comparison (Robust)', fontsize=18, fontweight='bold', pad=20)
        ax1.set_ylabel('NAV', fontsize=14)
        ax1.legend(fontsize=12, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 回撤对比
        for i, (strategy_name, data) in enumerate(all_data.items()):
            if strategy_name == 'Benchmark':
                benchmark_peak = data['benchmark_nav'].expanding().max()
                benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
                ax2.plot(data.index, benchmark_drawdown * 100, label=strategy_name, 
                        linewidth=2, color=colors[i], alpha=0.9, linestyle='--')
            else:
                strategy_peak = data['strategy_nav'].expanding().max()
                strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak
                ax2.plot(data.index, strategy_drawdown * 100, label=strategy_name, 
                        linewidth=2, color=colors[i], alpha=0.9)

        ax2.set_title('All Strategies Drawdown Comparison', fontsize=16)
        ax2.set_ylabel('Drawdown (%)', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/All_Strategies_Comparison_Robust.png", dpi=300, bbox_inches='tight')
        plt.close()
