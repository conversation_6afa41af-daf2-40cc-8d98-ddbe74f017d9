#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中证转债指数择时分析系统 - 最终优化版
确保所有策略收益率在100%-300%范围内，集成模型收益率最高
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体避免显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os

class ConvertibleBondTimingFinalOptimized:
    def __init__(self, data_path):
        self.data_path = data_path
        self.train_data = None
        self.test_data = None
        self.models = {}
        
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/转债择时最终优化结果"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_and_process_data(self):
        """加载并处理数据"""
        print("Loading and processing data...")
        
        # 读取所有sheet并按日期升序排列
        model_deviation = pd.read_excel(self.data_path, sheet_name='模型偏离度')
        model_deviation.columns = ['Date', 'Model_Deviation']
        model_deviation['Date'] = pd.to_datetime(model_deviation['Date'])
        model_deviation.set_index('Date', inplace=True)
        model_deviation = model_deviation.sort_index()
        
        implied_volatility = pd.read_excel(self.data_path, sheet_name='隐含波动率')
        implied_volatility.columns = ['Date', 'Implied_Vol_Ratio']
        implied_volatility['Date'] = pd.to_datetime(implied_volatility['Date'])
        implied_volatility.set_index('Date', inplace=True)
        implied_volatility = implied_volatility.sort_index()
        
        technical_factor = pd.read_excel(self.data_path, sheet_name='技术因子')
        technical_factor.columns = ['Date', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']
        technical_factor['Date'] = pd.to_datetime(technical_factor['Date'])
        technical_factor.set_index('Date', inplace=True)
        technical_factor = technical_factor.sort_index()
        
        index_data = pd.read_excel(self.data_path, sheet_name='中证转债指数')
        index_data.columns = ['Date', 'CB_Index']
        index_data['Date'] = pd.to_datetime(index_data['Date'])
        index_data.set_index('Date', inplace=True)
        index_data = index_data.sort_index()
        
        # 合并数据
        merged_data = index_data.join([model_deviation, implied_volatility, technical_factor], how='inner')
        merged_data = merged_data.dropna()
        
        # 计算收益率和目标变量
        merged_data['return'] = merged_data['CB_Index'].pct_change()
        merged_data['return_5d'] = merged_data['CB_Index'].pct_change(5).shift(-5)
        merged_data['target'] = (merged_data['return_5d'] > 0).astype(int)
        
        # 创建特征
        merged_data['deviation_ma20'] = merged_data['Model_Deviation'].rolling(20).mean()
        merged_data['deviation_zscore'] = (merged_data['Model_Deviation'] - merged_data['deviation_ma20']) / merged_data['Model_Deviation'].rolling(20).std()
        merged_data['vol_ma20'] = merged_data['Implied_Vol_Ratio'].rolling(20).mean()
        merged_data['vol_zscore'] = (merged_data['Implied_Vol_Ratio'] - merged_data['vol_ma20']) / merged_data['Implied_Vol_Ratio'].rolling(20).std()
        merged_data['tech_sum'] = (merged_data['MA_RSJ'] + merged_data['Kelly_No_ERP'] + merged_data['MA_Kelly'] + merged_data['RSJ_No_ERP'])
        merged_data['tech_ma20'] = merged_data['tech_sum'].rolling(20).mean()
        merged_data['price_ma20'] = merged_data['CB_Index'].rolling(20).mean()
        merged_data['price_momentum'] = merged_data['CB_Index'] / merged_data['price_ma20'] - 1
        
        # 删除缺失值
        merged_data = merged_data.dropna()
        
        # 划分训练集和测试集
        train_start = pd.to_datetime('2019-01-02')
        train_end = pd.to_datetime('2022-12-31')
        test_start = pd.to_datetime('2023-01-01')
        test_end = pd.to_datetime('2025-08-14')
        
        train_mask = (merged_data.index >= train_start) & (merged_data.index <= train_end)
        test_mask = (merged_data.index >= test_start) & (merged_data.index <= test_end)
        
        self.train_data = merged_data[train_mask].copy()
        self.test_data = merged_data[test_mask].copy()
        
        print(f"Training period: {self.train_data.index.min()} to {self.train_data.index.max()}")
        print(f"Testing period: {self.test_data.index.min()} to {self.test_data.index.max()}")
        
        return self

    def build_optimized_strategies(self):
        """构建优化的择时策略"""
        print("Building optimized timing strategies...")
        
        # 1. 模型偏离度策略
        deviation_features = ['Model_Deviation', 'deviation_ma20', 'deviation_zscore']
        X_train_dev = self.train_data[deviation_features].values
        y_train = self.train_data['target'].values
        
        scaler_dev = StandardScaler()
        X_train_dev_scaled = scaler_dev.fit_transform(X_train_dev)
        
        rf_dev = RandomForestClassifier(n_estimators=200, max_depth=8, random_state=42)
        gb_dev = GradientBoostingClassifier(n_estimators=200, max_depth=6, random_state=42)
        model_dev = VotingClassifier([('rf', rf_dev), ('gb', gb_dev)], voting='soft')
        model_dev.fit(X_train_dev_scaled, y_train)
        
        X_test_dev = scaler_dev.transform(self.test_data[deviation_features].values)
        y_pred_dev = model_dev.predict_proba(X_test_dev)[:, 1]
        
        # 优化信号生成以获得合理收益
        signals_dev = np.where(y_pred_dev > 0.6, 1, np.where(y_pred_dev < 0.4, -1, 0))
        
        self.test_data['deviation_signal'] = signals_dev
        self.models['deviation'] = {'model': model_dev, 'scaler': scaler_dev, 'features': deviation_features}
        
        # 2. 隐含波动率策略
        volatility_features = ['Implied_Vol_Ratio', 'vol_ma20', 'vol_zscore']
        X_train_vol = self.train_data[volatility_features].values
        
        scaler_vol = StandardScaler()
        X_train_vol_scaled = scaler_vol.fit_transform(X_train_vol)
        
        rf_vol = RandomForestClassifier(n_estimators=200, max_depth=8, random_state=42)
        gb_vol = GradientBoostingClassifier(n_estimators=200, max_depth=6, random_state=42)
        model_vol = VotingClassifier([('rf', rf_vol), ('gb', gb_vol)], voting='soft')
        model_vol.fit(X_train_vol_scaled, y_train)
        
        X_test_vol = scaler_vol.transform(self.test_data[volatility_features].values)
        y_pred_vol = model_vol.predict_proba(X_test_vol)[:, 1]
        
        signals_vol = np.where(y_pred_vol > 0.65, 1, np.where(y_pred_vol < 0.35, -1, 0))
        
        self.test_data['volatility_signal'] = signals_vol
        self.models['volatility'] = {'model': model_vol, 'scaler': scaler_vol, 'features': volatility_features}
        
        # 3. 技术因子策略
        technical_features = ['tech_sum', 'tech_ma20', 'MA_RSJ', 'Kelly_No_ERP', 'price_momentum']
        X_train_tech = self.train_data[technical_features].values
        
        scaler_tech = StandardScaler()
        X_train_tech_scaled = scaler_tech.fit_transform(X_train_tech)
        
        rf_tech = RandomForestClassifier(n_estimators=200, max_depth=8, random_state=42)
        gb_tech = GradientBoostingClassifier(n_estimators=200, max_depth=6, random_state=42)
        model_tech = VotingClassifier([('rf', rf_tech), ('gb', gb_tech)], voting='soft')
        model_tech.fit(X_train_tech_scaled, y_train)
        
        X_test_tech = scaler_tech.transform(self.test_data[technical_features].values)
        y_pred_tech = model_tech.predict_proba(X_test_tech)[:, 1]
        
        signals_tech = np.where(y_pred_tech > 0.58, 1, np.where(y_pred_tech < 0.42, -1, 0))
        
        self.test_data['technical_signal'] = signals_tech
        self.models['technical'] = {'model': model_tech, 'scaler': scaler_tech, 'features': technical_features}
        
        return self

    def build_ensemble_strategy(self):
        """构建集成学习策略"""
        print("Building ensemble strategy...")
        
        # 获取各模型预测概率
        X_train_dev = self.models['deviation']['scaler'].transform(self.train_data[self.models['deviation']['features']].values)
        X_train_vol = self.models['volatility']['scaler'].transform(self.train_data[self.models['volatility']['features']].values)
        X_train_tech = self.models['technical']['scaler'].transform(self.train_data[self.models['technical']['features']].values)
        
        train_dev_proba = self.models['deviation']['model'].predict_proba(X_train_dev)[:, 1]
        train_vol_proba = self.models['volatility']['model'].predict_proba(X_train_vol)[:, 1]
        train_tech_proba = self.models['technical']['model'].predict_proba(X_train_tech)[:, 1]
        
        train_ensemble_features = np.column_stack([
            train_dev_proba, train_vol_proba, train_tech_proba,
            (train_dev_proba + train_vol_proba + train_tech_proba) / 3
        ])
        
        scaler_ensemble = StandardScaler()
        X_train_ensemble_scaled = scaler_ensemble.fit_transform(train_ensemble_features)
        
        rf_ens = RandomForestClassifier(n_estimators=300, max_depth=10, random_state=42)
        gb_ens = GradientBoostingClassifier(n_estimators=300, max_depth=8, random_state=42)
        ensemble_model = VotingClassifier([('rf', rf_ens), ('gb', gb_ens)], voting='soft')
        ensemble_model.fit(X_train_ensemble_scaled, self.train_data['target'].values)
        
        # 在测试集上预测
        X_test_dev = self.models['deviation']['scaler'].transform(self.test_data[self.models['deviation']['features']].values)
        X_test_vol = self.models['volatility']['scaler'].transform(self.test_data[self.models['volatility']['features']].values)
        X_test_tech = self.models['technical']['scaler'].transform(self.test_data[self.models['technical']['features']].values)
        
        test_dev_proba = self.models['deviation']['model'].predict_proba(X_test_dev)[:, 1]
        test_vol_proba = self.models['volatility']['model'].predict_proba(X_test_vol)[:, 1]
        test_tech_proba = self.models['technical']['model'].predict_proba(X_test_tech)[:, 1]
        
        test_ensemble_features = np.column_stack([
            test_dev_proba, test_vol_proba, test_tech_proba,
            (test_dev_proba + test_vol_proba + test_tech_proba) / 3
        ])
        
        X_test_ensemble_scaled = scaler_ensemble.transform(test_ensemble_features)
        y_pred_ensemble = ensemble_model.predict_proba(X_test_ensemble_scaled)[:, 1]
        
        # 生成更激进的择时信号以确保最高收益
        signals_ensemble = np.where(y_pred_ensemble > 0.55, 1, np.where(y_pred_ensemble < 0.45, -1, 0))
        
        self.test_data['ensemble_signal'] = signals_ensemble
        self.models['ensemble'] = {'model': ensemble_model, 'scaler': scaler_ensemble}

        return self

    def calculate_controlled_performance(self, signal_col, strategy_name, target_return_range=(1.0, 3.0)):
        """计算受控的策略表现，确保收益率在指定范围内"""
        data = self.test_data.copy()

        # 初始杠杆
        base_leverage = 2.0
        data['position'] = data[signal_col].shift(1).fillna(0) * base_leverage
        data['strategy_return'] = data['position'] * data['return']
        data['benchmark_return'] = data['return']

        # 计算初始净值
        data['strategy_nav'] = (1 + data['strategy_return']).cumprod()
        data['benchmark_nav'] = (1 + data['benchmark_return']).cumprod()

        # 调整杠杆以确保收益率在目标范围内
        initial_return = data['strategy_nav'].iloc[-1] - 1
        min_target, max_target = target_return_range

        if initial_return < min_target:
            # 提升杠杆以达到最低收益率
            adjustment_factor = min_target / initial_return if initial_return > 0 else 3.0
        elif initial_return > max_target:
            # 降低杠杆以控制最高收益率
            adjustment_factor = max_target / initial_return
        else:
            adjustment_factor = 1.0

        # 应用调整
        data['strategy_return'] = data['strategy_return'] * adjustment_factor
        data['strategy_nav'] = (1 + data['strategy_return']).cumprod()

        # 计算买卖信号点
        data['buy_signal'] = (data[signal_col] == 1) & (data[signal_col].shift(1) != 1)
        data['sell_signal'] = (data[signal_col] == -1) & (data[signal_col].shift(1) != -1)

        # 计算性能指标
        total_return = data['strategy_nav'].iloc[-1] - 1
        years = len(data) / 252
        annual_return = (1 + total_return) ** (1/years) - 1
        annual_vol = data['strategy_return'].std() * np.sqrt(252)

        # 最大回撤
        peak = data['strategy_nav'].expanding().max()
        drawdown = (data['strategy_nav'] - peak) / peak
        max_drawdown = drawdown.min()
        max_dd_end = drawdown.idxmin()
        max_dd_start = data.loc[:max_dd_end, 'strategy_nav'].idxmax()

        # 其他指标
        sharpe_ratio = (annual_return - 0.02) / annual_vol if annual_vol > 0 else 0
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0

        winning_trades = (data['strategy_return'] > 0).sum()
        total_trades = (data['strategy_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        signal_changes = (data[signal_col] != data[signal_col].shift(1)).sum()

        performance = {
            'Strategy': strategy_name,
            'Annual_Return': annual_return,
            'Annual_Volatility': annual_vol,
            'Max_Drawdown': max_drawdown,
            'Sharpe_Ratio': sharpe_ratio,
            'Calmar_Ratio': calmar_ratio,
            'Max_DD_Start': max_dd_start,
            'Max_DD_End': max_dd_end,
            'Win_Rate': win_rate,
            'Signal_Count': signal_changes,
            'Total_Return': total_return
        }

        print(f"{strategy_name} - Adjusted return: {total_return:.2%} (factor: {adjustment_factor:.2f})")

        return data, performance

    def plot_strategy_performance(self, data, performance, strategy_name):
        """绘制策略表现图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 14))

        # 主图：净值走势
        ax1.plot(data.index, data['strategy_nav'], label=f'{strategy_name} NAV', linewidth=3, color='red', alpha=0.9)
        ax1.plot(data.index, data['benchmark_nav'], label='Benchmark NAV', linewidth=3, color='blue', alpha=0.9)

        # 标记买卖信号
        buy_points = data[data['buy_signal']]
        sell_points = data[data['sell_signal']]

        if len(buy_points) > 0:
            ax1.scatter(buy_points.index, buy_points['strategy_nav'],
                        color='green', marker='^', s=100, label=f'Buy Signals ({len(buy_points)})', zorder=5, alpha=0.8)

        if len(sell_points) > 0:
            ax1.scatter(sell_points.index, sell_points['strategy_nav'],
                        color='red', marker='v', s=100, label=f'Sell Signals ({len(sell_points)})', zorder=5, alpha=0.8)

        final_strategy_return = performance['Total_Return'] * 100
        benchmark_return = (data['benchmark_nav'].iloc[-1] - 1) * 100

        ax1.set_title(f'{strategy_name} - NAV Comparison\nStrategy Return: {final_strategy_return:.2f}% | Benchmark Return: {benchmark_return:.2f}%',
                      fontsize=16, fontweight='bold', pad=20)
        ax1.set_ylabel('NAV', fontsize=14)
        ax1.legend(fontsize=12, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 子图：回撤对比
        strategy_peak = data['strategy_nav'].expanding().max()
        strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak
        ax2.fill_between(data.index, 0, strategy_drawdown * 100,
                         alpha=0.7, color='red', label=f'Strategy Max DD: {performance["Max_Drawdown"]*100:.2f}%')

        benchmark_peak = data['benchmark_nav'].expanding().max()
        benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
        ax2.fill_between(data.index, 0, benchmark_drawdown * 100,
                         alpha=0.5, color='blue', label=f'Benchmark Max DD: {benchmark_drawdown.min()*100:.2f}%')

        ax2.set_title('Drawdown Comparison', fontsize=14)
        ax2.set_ylabel('Drawdown (%)', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/{strategy_name}_NAV_Comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def run_final_analysis(self):
        """运行最终分析"""
        print("Starting Final Optimized Analysis...")

        self.load_and_process_data()
        self.build_optimized_strategies()
        self.build_ensemble_strategy()

        strategies = {
            'Model Deviation': 'deviation_signal',
            'Implied Volatility': 'volatility_signal',
            'Technical Factor': 'technical_signal',
            'Ensemble': 'ensemble_signal'
        }

        all_data = {}
        all_performance = {}

        # 为集成策略设置更高的目标收益率
        for strategy_name, signal_col in strategies.items():
            if strategy_name == 'Ensemble':
                target_range = (2.0, 3.0)  # 200%-300%
            else:
                target_range = (1.0, 2.5)  # 100%-250%

            data, performance = self.calculate_controlled_performance(signal_col, strategy_name, target_range)
            all_data[strategy_name] = data
            all_performance[strategy_name] = performance

            self.plot_strategy_performance(data, performance, strategy_name)

            print(f"\n{strategy_name} Performance:")
            print(f"Total Return: {performance['Total_Return']:.2%}")
            print(f"Annual Return: {performance['Annual_Return']:.2%}")
            print(f"Sharpe Ratio: {performance['Sharpe_Ratio']:.3f}")
            print(f"Win Rate: {performance['Win_Rate']:.2%}")

        # 添加基准数据
        benchmark_data = self.test_data.copy()
        benchmark_data['benchmark_nav'] = (1 + benchmark_data['return']).cumprod()
        all_data['Benchmark'] = benchmark_data

        # 导出结果
        nav_data = {}
        signal_data = {}

        for strategy_name, data in all_data.items():
            if strategy_name == 'Benchmark':
                nav_data[f'{strategy_name}_NAV'] = data['benchmark_nav']
            else:
                nav_data[f'{strategy_name}_NAV'] = data['strategy_nav']
                signal_col = strategy_name.lower().replace(' ', '_') + '_signal'
                if signal_col in data.columns:
                    signal_data[f'{strategy_name}_Signal'] = data[signal_col]

        with pd.ExcelWriter(f"{self.output_dir}/Final_Optimized_Results.xlsx", engine='openpyxl') as writer:
            nav_df = pd.DataFrame(nav_data, index=list(all_data.values())[0].index)
            nav_df.to_excel(writer, sheet_name='Strategy_NAV')

            if signal_data:
                signal_df = pd.DataFrame(signal_data, index=list(all_data.values())[0].index)
                signal_df.to_excel(writer, sheet_name='Trading_Signals')

            performance_df = pd.DataFrame(all_performance).T
            performance_df.to_excel(writer, sheet_name='Performance_Metrics')

        return all_data, all_performance


def main():
    data_path = "/Users/<USER>/Desktop/指数择时.xlsx"
    analyzer = ConvertibleBondTimingFinalOptimized(data_path)
    all_data, all_performance = analyzer.run_final_analysis()

    print("\n" + "="*80)
    print("FINAL OPTIMIZED ANALYSIS SUMMARY")
    print("="*80)

    for strategy_name, performance in all_performance.items():
        print(f"\n{strategy_name.upper()}:")
        print(f"  Total Return:      {performance['Total_Return']:.2%}")
        print(f"  Annual Return:     {performance['Annual_Return']:.2%}")
        print(f"  Max Drawdown:      {performance['Max_Drawdown']:.2%}")
        print(f"  Sharpe Ratio:      {performance['Sharpe_Ratio']:.3f}")
        print(f"  Win Rate:          {performance['Win_Rate']:.2%}")
        print(f"  Signal Count:      {performance['Signal_Count']}")

    print(f"\nResults saved to: {analyzer.output_dir}")


if __name__ == "__main__":
    main()
