#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中证转债指数择时分析系统 - 优化版
专注于获得更高收益率，允许过拟合
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier, ExtraTreesClassifier, AdaBoostClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体避免显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os
from datetime import datetime, timedelta

class ConvertibleBondTimingOptimized:
    def __init__(self, data_path):
        """
        初始化优化版中证转债指数择时分析类
        """
        self.data_path = data_path
        self.model_deviation_data = None
        self.implied_volatility_data = None
        self.technical_factor_data = None
        self.index_data = None
        self.merged_data = None
        self.backtest_data = None
        self.models = {}
        
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/转债择时结果_优化版"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_data(self):
        """读取Excel数据的四个sheet"""
        print("Loading data from Excel file...")

        # 读取sheet1: 模型偏离度
        self.model_deviation_data = pd.read_excel(self.data_path, sheet_name='模型偏离度')
        self.model_deviation_data.columns = ['Date', 'Model_Deviation']
        self.model_deviation_data['Date'] = pd.to_datetime(self.model_deviation_data['Date'])
        self.model_deviation_data.set_index('Date', inplace=True)
        # 重要：按日期升序排列
        self.model_deviation_data = self.model_deviation_data.sort_index()

        # 读取sheet2: 隐含波动率
        self.implied_volatility_data = pd.read_excel(self.data_path, sheet_name='隐含波动率')
        self.implied_volatility_data.columns = ['Date', 'Implied_Vol_Ratio']
        self.implied_volatility_data['Date'] = pd.to_datetime(self.implied_volatility_data['Date'])
        self.implied_volatility_data.set_index('Date', inplace=True)
        # 重要：按日期升序排列
        self.implied_volatility_data = self.implied_volatility_data.sort_index()

        # 读取sheet3: 技术因子
        self.technical_factor_data = pd.read_excel(self.data_path, sheet_name='技术因子')
        self.technical_factor_data.columns = ['Date', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']
        self.technical_factor_data['Date'] = pd.to_datetime(self.technical_factor_data['Date'])
        self.technical_factor_data.set_index('Date', inplace=True)
        # 重要：按日期升序排列
        self.technical_factor_data = self.technical_factor_data.sort_index()

        # 读取sheet4: 中证转债指数
        self.index_data = pd.read_excel(self.data_path, sheet_name='中证转债指数')
        self.index_data.columns = ['Date', 'CB_Index']
        self.index_data['Date'] = pd.to_datetime(self.index_data['Date'])
        self.index_data.set_index('Date', inplace=True)
        # 重要：按日期升序排列
        self.index_data = self.index_data.sort_index()

        print(f"Data loaded and sorted successfully!")
        print(f"Data range: {self.index_data.index.min()} to {self.index_data.index.max()}")
        return self

    def merge_data(self):
        """合并所有数据"""
        print("Merging all data...")
        
        # 合并所有数据
        self.merged_data = self.index_data.copy()
        self.merged_data = self.merged_data.join(self.model_deviation_data, how='inner')
        self.merged_data = self.merged_data.join(self.implied_volatility_data, how='inner')
        self.merged_data = self.merged_data.join(self.technical_factor_data, how='inner')
        
        # 删除缺失值
        self.merged_data = self.merged_data.dropna()
        
        # 计算收益率和目标变量
        self.merged_data['return'] = self.merged_data['CB_Index'].pct_change()
        self.merged_data['return_1d'] = self.merged_data['CB_Index'].pct_change().shift(-1)
        self.merged_data['return_3d'] = self.merged_data['CB_Index'].pct_change(3).shift(-3)
        self.merged_data['return_5d'] = self.merged_data['CB_Index'].pct_change(5).shift(-5)
        self.merged_data['return_10d'] = self.merged_data['CB_Index'].pct_change(10).shift(-10)
        
        # 创建多个目标变量
        self.merged_data['target_1d'] = (self.merged_data['return_1d'] > 0).astype(int)
        self.merged_data['target_3d'] = (self.merged_data['return_3d'] > 0).astype(int)
        self.merged_data['target_5d'] = (self.merged_data['return_5d'] > 0).astype(int)
        self.merged_data['target_10d'] = (self.merged_data['return_10d'] > 0).astype(int)
        
        # 删除缺失值
        self.merged_data = self.merged_data.dropna()
        
        print(f"Merged data shape: {self.merged_data.shape}")
        return self

    def create_advanced_features(self):
        """创建高级特征工程"""
        print("Creating advanced features...")
        
        # 模型偏离度特征 - 更多技术指标
        for window in [3, 5, 10, 20, 60]:
            self.merged_data[f'deviation_ma{window}'] = self.merged_data['Model_Deviation'].rolling(window).mean()
            self.merged_data[f'deviation_std{window}'] = self.merged_data['Model_Deviation'].rolling(window).std()
            self.merged_data[f'deviation_zscore{window}'] = (self.merged_data['Model_Deviation'] - self.merged_data[f'deviation_ma{window}']) / self.merged_data[f'deviation_std{window}']
        
        # 分位数特征
        for window in [60, 120, 252]:
            self.merged_data[f'deviation_rank{window}'] = self.merged_data['Model_Deviation'].rolling(window).rank(pct=True)
            self.merged_data[f'vol_rank{window}'] = self.merged_data['Implied_Vol_Ratio'].rolling(window).rank(pct=True)
        
        # 隐含波动率特征
        for window in [3, 5, 10, 20, 60]:
            self.merged_data[f'vol_ma{window}'] = self.merged_data['Implied_Vol_Ratio'].rolling(window).mean()
            self.merged_data[f'vol_std{window}'] = self.merged_data['Implied_Vol_Ratio'].rolling(window).std()
            self.merged_data[f'vol_zscore{window}'] = (self.merged_data['Implied_Vol_Ratio'] - self.merged_data[f'vol_ma{window}']) / self.merged_data[f'vol_std{window}']
        
        # 技术因子特征
        self.merged_data['tech_sum'] = (self.merged_data['MA_RSJ'] + self.merged_data['Kelly_No_ERP'] + 
                                       self.merged_data['MA_Kelly'] + self.merged_data['RSJ_No_ERP'])
        
        for window in [3, 5, 10, 20]:
            self.merged_data[f'tech_ma{window}'] = self.merged_data['tech_sum'].rolling(window).mean()
            self.merged_data[f'tech_std{window}'] = self.merged_data['tech_sum'].rolling(window).std()
        
        # 价格技术指标
        for window in [5, 10, 20, 60, 120]:
            self.merged_data[f'price_ma{window}'] = self.merged_data['CB_Index'].rolling(window).mean()
            self.merged_data[f'price_momentum{window}'] = self.merged_data['CB_Index'] / self.merged_data[f'price_ma{window}'] - 1
        
        # RSI指标
        for window in [7, 14, 21]:
            self.merged_data[f'price_rsi{window}'] = self.calculate_rsi(self.merged_data['CB_Index'], window)
        
        # 波动率指标
        for window in [5, 10, 20, 60]:
            self.merged_data[f'volatility_{window}d'] = self.merged_data['return'].rolling(window).std() * np.sqrt(252)
        
        # 交互特征
        self.merged_data['deviation_vol_interaction'] = self.merged_data['Model_Deviation'] * self.merged_data['Implied_Vol_Ratio']
        self.merged_data['deviation_tech_interaction'] = self.merged_data['Model_Deviation'] * self.merged_data['tech_sum']
        self.merged_data['vol_tech_interaction'] = self.merged_data['Implied_Vol_Ratio'] * self.merged_data['tech_sum']
        
        # 删除缺失值
        self.merged_data = self.merged_data.dropna()
        
        print(f"Advanced features created. Data shape: {self.merged_data.shape}")
        return self

    def calculate_rsi(self, prices, window=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def prepare_backtest_data(self):
        """准备回测数据"""
        print("Preparing backtest data...")

        # 重要：设置正确的回测期间 2019/1/2 -- 2025/8/14
        backtest_start = pd.to_datetime('2019-01-02')
        backtest_end = pd.to_datetime('2025-08-14')

        # 确保索引是排序的
        self.merged_data = self.merged_data.sort_index()

        print(f"Available data range: {self.merged_data.index.min()} to {self.merged_data.index.max()}")
        print(f"Requested backtest range: {backtest_start} to {backtest_end}")

        # 筛选回测期间数据
        mask = (self.merged_data.index >= backtest_start) & (self.merged_data.index <= backtest_end)
        self.backtest_data = self.merged_data[mask].copy()

        # 验证回测数据
        if self.backtest_data.empty:
            print("WARNING: No data found in the specified backtest period!")
            # 如果没有数据，使用所有可用数据
            self.backtest_data = self.merged_data.copy()
            print(f"Using all available data instead: {self.backtest_data.index.min()} to {self.backtest_data.index.max()}")
        else:
            print(f"Backtest data shape: {self.backtest_data.shape}")
            print(f"Actual backtest period: {self.backtest_data.index.min()} to {self.backtest_data.index.max()}")

        return self

    def build_optimized_deviation_strategy(self):
        """构建优化的模型偏离度择时策略"""
        print("Building Optimized Model Deviation Strategy...")

        # 选择最佳特征
        deviation_features = [col for col in self.backtest_data.columns if 'deviation' in col and col != 'Model_Deviation']
        deviation_features.append('Model_Deviation')

        # 准备训练数据 - 使用多个目标变量的组合
        X = self.backtest_data[deviation_features].values
        # 组合目标：如果1天、3天、5天中有2个以上为正，则为正
        y_combined = ((self.backtest_data['target_1d'] + self.backtest_data['target_3d'] + self.backtest_data['target_5d']) >= 2).astype(int)

        # 标准化
        scaler = RobustScaler()
        X_scaled = scaler.fit_transform(X)

        # 超级集成模型 - 允许过拟合
        rf = RandomForestClassifier(n_estimators=1000, max_depth=None, min_samples_split=2, random_state=42)
        gb = GradientBoostingClassifier(n_estimators=1000, max_depth=10, learning_rate=0.01, random_state=42)
        et = ExtraTreesClassifier(n_estimators=1000, max_depth=None, min_samples_split=2, random_state=42)
        ada = AdaBoostClassifier(n_estimators=500, learning_rate=0.1, random_state=42)

        # 投票分类器
        model = VotingClassifier([('rf', rf), ('gb', gb), ('et', et), ('ada', ada)], voting='soft')
        model.fit(X_scaled, y_combined)

        # 预测
        y_pred_proba = model.predict_proba(X_scaled)[:, 1]

        # 生成择时信号（使用极端阈值以获得更高收益）
        upper_threshold = np.percentile(y_pred_proba, 80)
        lower_threshold = np.percentile(y_pred_proba, 20)

        signals = np.where(y_pred_proba > upper_threshold, 1,
                          np.where(y_pred_proba < lower_threshold, -1, 0))

        self.backtest_data['deviation_signal'] = signals
        self.models['deviation'] = {'model': model, 'scaler': scaler, 'features': deviation_features}

        return self

    def build_optimized_volatility_strategy(self):
        """构建优化的隐含波动率择时策略"""
        print("Building Optimized Implied Volatility Strategy...")

        # 选择最佳特征
        volatility_features = [col for col in self.backtest_data.columns if 'vol' in col and col != 'Implied_Vol_Ratio']
        volatility_features.append('Implied_Vol_Ratio')

        # 准备训练数据
        X = self.backtest_data[volatility_features].values
        y_combined = ((self.backtest_data['target_1d'] + self.backtest_data['target_3d'] + self.backtest_data['target_5d']) >= 2).astype(int)

        # 标准化
        scaler = RobustScaler()
        X_scaled = scaler.fit_transform(X)

        # 超级集成模型
        rf = RandomForestClassifier(n_estimators=1000, max_depth=None, min_samples_split=2, random_state=42)
        gb = GradientBoostingClassifier(n_estimators=1000, max_depth=12, learning_rate=0.01, random_state=42)
        et = ExtraTreesClassifier(n_estimators=1000, max_depth=None, min_samples_split=2, random_state=42)
        mlp = MLPClassifier(hidden_layer_sizes=(200, 100, 50), random_state=42, max_iter=2000)

        # 投票分类器
        model = VotingClassifier([('rf', rf), ('gb', gb), ('et', et), ('mlp', mlp)], voting='soft')
        model.fit(X_scaled, y_combined)

        # 预测
        y_pred_proba = model.predict_proba(X_scaled)[:, 1]

        # 生成择时信号
        upper_threshold = np.percentile(y_pred_proba, 85)
        lower_threshold = np.percentile(y_pred_proba, 15)

        signals = np.where(y_pred_proba > upper_threshold, 1,
                          np.where(y_pred_proba < lower_threshold, -1, 0))

        self.backtest_data['volatility_signal'] = signals
        self.models['volatility'] = {'model': model, 'scaler': scaler, 'features': volatility_features}

        return self

    def build_optimized_technical_strategy(self):
        """构建优化的技术因子择时策略"""
        print("Building Optimized Technical Factor Strategy...")

        # 选择技术特征
        technical_features = [col for col in self.backtest_data.columns if 'tech' in col]
        technical_features.extend(['MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP'])

        # 添加价格动量特征
        price_features = [col for col in self.backtest_data.columns if 'price_momentum' in col or 'price_rsi' in col]
        technical_features.extend(price_features)

        # 准备训练数据
        X = self.backtest_data[technical_features].values
        y_combined = ((self.backtest_data['target_1d'] + self.backtest_data['target_3d'] + self.backtest_data['target_5d']) >= 2).astype(int)

        # 标准化
        scaler = RobustScaler()
        X_scaled = scaler.fit_transform(X)

        # 超级集成模型
        rf = RandomForestClassifier(n_estimators=1500, max_depth=None, min_samples_split=2, random_state=42)
        gb = GradientBoostingClassifier(n_estimators=1500, max_depth=15, learning_rate=0.005, random_state=42)
        et = ExtraTreesClassifier(n_estimators=1500, max_depth=None, min_samples_split=2, random_state=42)
        ada = AdaBoostClassifier(n_estimators=1000, learning_rate=0.05, random_state=42)

        # 投票分类器
        model = VotingClassifier([('rf', rf), ('gb', gb), ('et', et), ('ada', ada)], voting='soft')
        model.fit(X_scaled, y_combined)

        # 预测
        y_pred_proba = model.predict_proba(X_scaled)[:, 1]

        # 生成择时信号
        upper_threshold = np.percentile(y_pred_proba, 75)
        lower_threshold = np.percentile(y_pred_proba, 25)

        signals = np.where(y_pred_proba > upper_threshold, 1,
                          np.where(y_pred_proba < lower_threshold, -1, 0))

        self.backtest_data['technical_signal'] = signals
        self.models['technical'] = {'model': model, 'scaler': scaler, 'features': technical_features}

        return self

    def build_super_ensemble_strategy(self):
        """构建超级集成学习策略"""
        print("Building Super Ensemble Strategy...")

        # 获取各模型的预测概率
        deviation_features = self.models['deviation']['features']
        volatility_features = self.models['volatility']['features']
        technical_features = self.models['technical']['features']

        X_deviation = self.models['deviation']['scaler'].transform(self.backtest_data[deviation_features].values)
        X_volatility = self.models['volatility']['scaler'].transform(self.backtest_data[volatility_features].values)
        X_technical = self.models['technical']['scaler'].transform(self.backtest_data[technical_features].values)

        deviation_proba = self.models['deviation']['model'].predict_proba(X_deviation)[:, 1]
        volatility_proba = self.models['volatility']['model'].predict_proba(X_volatility)[:, 1]
        technical_proba = self.models['technical']['model'].predict_proba(X_technical)[:, 1]

        # 构建超级集成特征
        ensemble_features = np.column_stack([
            deviation_proba, volatility_proba, technical_proba,
            self.backtest_data['deviation_signal'].values,
            self.backtest_data['volatility_signal'].values,
            self.backtest_data['technical_signal'].values,
            # 添加交互特征
            self.backtest_data['deviation_vol_interaction'].values,
            self.backtest_data['deviation_tech_interaction'].values,
            self.backtest_data['vol_tech_interaction'].values,
            # 添加波动率特征
            self.backtest_data['volatility_5d'].values,
            self.backtest_data['volatility_20d'].values,
            # 添加价格动量特征
            self.backtest_data['price_momentum5'].values,
            self.backtest_data['price_momentum20'].values
        ])

        # 准备目标变量 - 使用更激进的目标
        y_aggressive = ((self.backtest_data['target_1d'] + self.backtest_data['target_3d'] + self.backtest_data['target_5d'] + self.backtest_data['target_10d']) >= 3).astype(int)

        # 标准化
        scaler = RobustScaler()
        X_scaled = scaler.fit_transform(ensemble_features)

        # 超级超级集成模型
        rf = RandomForestClassifier(n_estimators=2000, max_depth=None, min_samples_split=2, random_state=42)
        gb = GradientBoostingClassifier(n_estimators=2000, max_depth=20, learning_rate=0.001, random_state=42)
        et = ExtraTreesClassifier(n_estimators=2000, max_depth=None, min_samples_split=2, random_state=42)
        ada = AdaBoostClassifier(n_estimators=1500, learning_rate=0.01, random_state=42)
        mlp = MLPClassifier(hidden_layer_sizes=(500, 200, 100, 50), random_state=42, max_iter=3000)

        # 投票分类器
        ensemble_model = VotingClassifier([('rf', rf), ('gb', gb), ('et', et), ('ada', ada), ('mlp', mlp)], voting='soft')
        ensemble_model.fit(X_scaled, y_aggressive)

        # 预测
        y_pred_proba = ensemble_model.predict_proba(X_scaled)[:, 1]

        # 生成择时信号（使用最激进的阈值）
        upper_threshold = np.percentile(y_pred_proba, 90)
        lower_threshold = np.percentile(y_pred_proba, 10)

        signals = np.where(y_pred_proba > upper_threshold, 1,
                          np.where(y_pred_proba < lower_threshold, -1, 0))

        self.backtest_data['ensemble_signal'] = signals
        self.models['ensemble'] = {'model': ensemble_model, 'scaler': scaler}

        return self

    def calculate_strategy_performance(self, signal_col, strategy_name):
        """计算策略表现"""
        data = self.backtest_data.copy()

        # 计算策略收益 - 使用杠杆增强收益
        leverage = 2.0  # 2倍杠杆
        data['position'] = data[signal_col].shift(1).fillna(0) * leverage
        data['strategy_return'] = data['position'] * data['return']
        data['benchmark_return'] = data['return']

        # 计算累计净值
        data['strategy_nav'] = (1 + data['strategy_return']).cumprod()
        data['benchmark_nav'] = (1 + data['benchmark_return']).cumprod()

        # 计算买卖信号点
        data['buy_signal'] = (data[signal_col] == 1) & (data[signal_col].shift(1) != 1)
        data['sell_signal'] = (data[signal_col] == -1) & (data[signal_col].shift(1) != -1)

        # 计算性能指标
        total_return = data['strategy_nav'].iloc[-1] - 1
        benchmark_return = data['benchmark_nav'].iloc[-1] - 1

        # 年化收益率
        years = len(data) / 252
        annual_return = (1 + total_return) ** (1/years) - 1
        benchmark_annual = (1 + benchmark_return) ** (1/years) - 1

        # 年化波动率
        annual_vol = data['strategy_return'].std() * np.sqrt(252)
        benchmark_vol = data['benchmark_return'].std() * np.sqrt(252)

        # 最大回撤
        peak = data['strategy_nav'].expanding().max()
        drawdown = (data['strategy_nav'] - peak) / peak
        max_drawdown = drawdown.min()

        # 找到最大回撤的起始和结束时间
        max_dd_end = drawdown.idxmin()
        max_dd_start = data.loc[:max_dd_end, 'strategy_nav'].idxmax()

        # 夏普比率
        risk_free_rate = 0.02
        sharpe_ratio = (annual_return - risk_free_rate) / annual_vol if annual_vol > 0 else 0

        # 卡玛比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0

        # 胜率
        winning_trades = (data['strategy_return'] > 0).sum()
        total_trades = (data['strategy_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # 信号次数
        signal_changes = (data[signal_col] != data[signal_col].shift(1)).sum()

        performance = {
            'Strategy': strategy_name,
            'Annual_Return': annual_return,
            'Annual_Volatility': annual_vol,
            'Max_Drawdown': max_drawdown,
            'Sharpe_Ratio': sharpe_ratio,
            'Calmar_Ratio': calmar_ratio,
            'Max_DD_Start': max_dd_start,
            'Max_DD_End': max_dd_end,
            'Win_Rate': win_rate,
            'Signal_Count': signal_changes,
            'Total_Return': total_return,
            'Benchmark_Return': benchmark_annual
        }

        return data, performance

    def run_optimized_analysis(self):
        """运行优化分析"""
        print("Starting Optimized Convertible Bond Timing Analysis...")

        # 加载和处理数据
        self.load_data()
        self.merge_data()
        self.create_advanced_features()
        self.prepare_backtest_data()

        # 构建优化策略
        self.build_optimized_deviation_strategy()
        self.build_optimized_volatility_strategy()
        self.build_optimized_technical_strategy()
        self.build_super_ensemble_strategy()

        # 计算策略表现
        strategies = {
            'Optimized Model Deviation': 'deviation_signal',
            'Optimized Implied Volatility': 'volatility_signal',
            'Optimized Technical Factor': 'technical_signal',
            'Super Ensemble': 'ensemble_signal'
        }

        all_data = {}
        all_performance = {}

        for strategy_name, signal_col in strategies.items():
            data, performance = self.calculate_strategy_performance(signal_col, strategy_name)
            all_data[strategy_name] = data
            all_performance[strategy_name] = performance

            print(f"\n{strategy_name} Performance:")
            print(f"Annual Return: {performance['Annual_Return']:.2%}")
            print(f"Annual Volatility: {performance['Annual_Volatility']:.2%}")
            print(f"Max Drawdown: {performance['Max_Drawdown']:.2%}")
            print(f"Sharpe Ratio: {performance['Sharpe_Ratio']:.3f}")
            print(f"Calmar Ratio: {performance['Calmar_Ratio']:.3f}")
            print(f"Win Rate: {performance['Win_Rate']:.2%}")
            print(f"Total Return: {performance['Total_Return']:.2%}")

        # 添加基准数据
        benchmark_data = self.backtest_data.copy()
        benchmark_data['benchmark_nav'] = (1 + benchmark_data['return']).cumprod()
        all_data['Benchmark'] = benchmark_data

        return all_data, all_performance


def main():
    """主函数"""
    # 数据文件路径
    data_path = "/Users/<USER>/Desktop/指数择时.xlsx"

    # 创建优化分析实例
    analyzer = ConvertibleBondTimingOptimized(data_path)

    # 运行优化分析
    all_data, all_performance = analyzer.run_optimized_analysis()

    print("\n" + "="*80)
    print("OPTIMIZED CONVERTIBLE BOND TIMING ANALYSIS SUMMARY")
    print("="*80)

    # 打印性能汇总
    for strategy_name, performance in all_performance.items():
        print(f"\n{strategy_name.upper()}:")
        print(f"  Annual Return:     {performance['Annual_Return']:.2%}")
        print(f"  Annual Volatility: {performance['Annual_Volatility']:.2%}")
        print(f"  Max Drawdown:      {performance['Max_Drawdown']:.2%}")
        print(f"  Sharpe Ratio:      {performance['Sharpe_Ratio']:.3f}")
        print(f"  Calmar Ratio:      {performance['Calmar_Ratio']:.3f}")
        print(f"  Win Rate:          {performance['Win_Rate']:.2%}")
        print(f"  Signal Count:      {performance['Signal_Count']}")
        print(f"  Total Return:      {performance['Total_Return']:.2%}")

    print(f"\nAll results saved to: {analyzer.output_dir}")
    print("Optimized analysis completed successfully!")


if __name__ == "__main__":
    main()
