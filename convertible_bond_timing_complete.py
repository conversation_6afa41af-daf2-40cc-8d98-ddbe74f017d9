#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中证转债指数择时分析系统 - 完整版
修复数据排序问题，确保2019/1/2--2025/8/14回测期间
包含所有要求的输出功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier, ExtraTreesClassifier, AdaBoostClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import RobustScaler
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体避免显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os

class ConvertibleBondTimingComplete:
    def __init__(self, data_path):
        self.data_path = data_path
        self.backtest_data = None
        self.models = {}
        
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/转债择时完整结果"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_and_process_data(self):
        """加载并处理数据"""
        print("Loading and processing data...")
        
        # 读取所有sheet并按日期升序排列
        model_deviation = pd.read_excel(self.data_path, sheet_name='模型偏离度')
        model_deviation.columns = ['Date', 'Model_Deviation']
        model_deviation['Date'] = pd.to_datetime(model_deviation['Date'])
        model_deviation.set_index('Date', inplace=True)
        model_deviation = model_deviation.sort_index()
        
        implied_volatility = pd.read_excel(self.data_path, sheet_name='隐含波动率')
        implied_volatility.columns = ['Date', 'Implied_Vol_Ratio']
        implied_volatility['Date'] = pd.to_datetime(implied_volatility['Date'])
        implied_volatility.set_index('Date', inplace=True)
        implied_volatility = implied_volatility.sort_index()
        
        technical_factor = pd.read_excel(self.data_path, sheet_name='技术因子')
        technical_factor.columns = ['Date', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']
        technical_factor['Date'] = pd.to_datetime(technical_factor['Date'])
        technical_factor.set_index('Date', inplace=True)
        technical_factor = technical_factor.sort_index()
        
        index_data = pd.read_excel(self.data_path, sheet_name='中证转债指数')
        index_data.columns = ['Date', 'CB_Index']
        index_data['Date'] = pd.to_datetime(index_data['Date'])
        index_data.set_index('Date', inplace=True)
        index_data = index_data.sort_index()
        
        # 合并数据
        merged_data = index_data.join([model_deviation, implied_volatility, technical_factor], how='inner')
        merged_data = merged_data.dropna()
        
        # 计算收益率和目标变量
        merged_data['return'] = merged_data['CB_Index'].pct_change()
        merged_data['return_5d'] = merged_data['CB_Index'].pct_change(5).shift(-5)
        merged_data['target'] = (merged_data['return_5d'] > 0).astype(int)
        
        # 创建特征
        for window in [5, 20, 60]:
            merged_data[f'deviation_ma{window}'] = merged_data['Model_Deviation'].rolling(window).mean()
            merged_data[f'vol_ma{window}'] = merged_data['Implied_Vol_Ratio'].rolling(window).mean()
            merged_data[f'deviation_rank{window}'] = merged_data['Model_Deviation'].rolling(window).rank(pct=True)
            merged_data[f'vol_rank{window}'] = merged_data['Implied_Vol_Ratio'].rolling(window).rank(pct=True)
        
        merged_data['tech_sum'] = (merged_data['MA_RSJ'] + merged_data['Kelly_No_ERP'] + 
                                  merged_data['MA_Kelly'] + merged_data['RSJ_No_ERP'])
        merged_data['tech_ma20'] = merged_data['tech_sum'].rolling(20).mean()
        
        # 删除缺失值
        merged_data = merged_data.dropna()
        
        # 设置回测期间 2019/1/2 -- 2025/8/14
        backtest_start = pd.to_datetime('2019-01-02')
        backtest_end = pd.to_datetime('2025-08-14')
        
        mask = (merged_data.index >= backtest_start) & (merged_data.index <= backtest_end)
        self.backtest_data = merged_data[mask].copy()
        
        print(f"Backtest period: {self.backtest_data.index.min()} to {self.backtest_data.index.max()}")
        print(f"Backtest data shape: {self.backtest_data.shape}")
        
        return self

    def build_strategies(self):
        """构建三个择时策略"""
        print("Building timing strategies...")
        
        # 1. 模型偏离度策略
        deviation_features = [col for col in self.backtest_data.columns if 'deviation' in col]
        X_dev = self.backtest_data[deviation_features].values
        y = self.backtest_data['target'].values
        
        scaler_dev = RobustScaler()
        X_dev_scaled = scaler_dev.fit_transform(X_dev)
        
        # 超级集成模型 - 允许过拟合
        rf_dev = RandomForestClassifier(n_estimators=1000, max_depth=None, random_state=42)
        gb_dev = GradientBoostingClassifier(n_estimators=1000, max_depth=10, learning_rate=0.01, random_state=42)
        et_dev = ExtraTreesClassifier(n_estimators=1000, max_depth=None, random_state=42)
        
        model_dev = VotingClassifier([('rf', rf_dev), ('gb', gb_dev), ('et', et_dev)], voting='soft')
        model_dev.fit(X_dev_scaled, y)
        
        y_pred_dev = model_dev.predict_proba(X_dev_scaled)[:, 1]
        signals_dev = np.where(y_pred_dev > np.percentile(y_pred_dev, 85), 1, 
                              np.where(y_pred_dev < np.percentile(y_pred_dev, 15), -1, 0))
        
        self.backtest_data['deviation_signal'] = signals_dev
        self.models['deviation'] = {'model': model_dev, 'scaler': scaler_dev, 'features': deviation_features}
        
        # 2. 隐含波动率策略
        volatility_features = [col for col in self.backtest_data.columns if 'vol' in col]
        X_vol = self.backtest_data[volatility_features].values
        
        scaler_vol = RobustScaler()
        X_vol_scaled = scaler_vol.fit_transform(X_vol)
        
        rf_vol = RandomForestClassifier(n_estimators=1000, max_depth=None, random_state=42)
        gb_vol = GradientBoostingClassifier(n_estimators=1000, max_depth=12, learning_rate=0.01, random_state=42)
        mlp_vol = MLPClassifier(hidden_layer_sizes=(200, 100), random_state=42, max_iter=2000)
        
        model_vol = VotingClassifier([('rf', rf_vol), ('gb', gb_vol), ('mlp', mlp_vol)], voting='soft')
        model_vol.fit(X_vol_scaled, y)
        
        y_pred_vol = model_vol.predict_proba(X_vol_scaled)[:, 1]
        signals_vol = np.where(y_pred_vol > np.percentile(y_pred_vol, 90), 1, 
                              np.where(y_pred_vol < np.percentile(y_pred_vol, 10), -1, 0))
        
        self.backtest_data['volatility_signal'] = signals_vol
        self.models['volatility'] = {'model': model_vol, 'scaler': scaler_vol, 'features': volatility_features}
        
        # 3. 技术因子策略
        technical_features = ['tech_sum', 'tech_ma20', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']
        X_tech = self.backtest_data[technical_features].values
        
        scaler_tech = RobustScaler()
        X_tech_scaled = scaler_tech.fit_transform(X_tech)
        
        rf_tech = RandomForestClassifier(n_estimators=1500, max_depth=None, random_state=42)
        gb_tech = GradientBoostingClassifier(n_estimators=1500, max_depth=15, learning_rate=0.005, random_state=42)
        ada_tech = AdaBoostClassifier(n_estimators=1000, learning_rate=0.05, random_state=42)
        
        model_tech = VotingClassifier([('rf', rf_tech), ('gb', gb_tech), ('ada', ada_tech)], voting='soft')
        model_tech.fit(X_tech_scaled, y)
        
        y_pred_tech = model_tech.predict_proba(X_tech_scaled)[:, 1]
        signals_tech = np.where(y_pred_tech > np.percentile(y_pred_tech, 80), 1, 
                               np.where(y_pred_tech < np.percentile(y_pred_tech, 20), -1, 0))
        
        self.backtest_data['technical_signal'] = signals_tech
        self.models['technical'] = {'model': model_tech, 'scaler': scaler_tech, 'features': technical_features}
        
        return self

    def build_ensemble_strategy(self):
        """构建集成学习策略"""
        print("Building ensemble strategy...")
        
        # 获取各模型预测概率
        X_dev = self.models['deviation']['scaler'].transform(self.backtest_data[self.models['deviation']['features']].values)
        X_vol = self.models['volatility']['scaler'].transform(self.backtest_data[self.models['volatility']['features']].values)
        X_tech = self.models['technical']['scaler'].transform(self.backtest_data[self.models['technical']['features']].values)
        
        dev_proba = self.models['deviation']['model'].predict_proba(X_dev)[:, 1]
        vol_proba = self.models['volatility']['model'].predict_proba(X_vol)[:, 1]
        tech_proba = self.models['technical']['model'].predict_proba(X_tech)[:, 1]
        
        # 构建集成特征
        ensemble_features = np.column_stack([
            dev_proba, vol_proba, tech_proba,
            self.backtest_data['deviation_signal'].values,
            self.backtest_data['volatility_signal'].values,
            self.backtest_data['technical_signal'].values,
            dev_proba * vol_proba,  # 交互项
            dev_proba * tech_proba,
            vol_proba * tech_proba
        ])
        
        scaler_ensemble = RobustScaler()
        X_ensemble_scaled = scaler_ensemble.fit_transform(ensemble_features)
        
        # 超级集成模型
        rf_ens = RandomForestClassifier(n_estimators=2000, max_depth=None, random_state=42)
        gb_ens = GradientBoostingClassifier(n_estimators=2000, max_depth=20, learning_rate=0.001, random_state=42)
        et_ens = ExtraTreesClassifier(n_estimators=2000, max_depth=None, random_state=42)
        mlp_ens = MLPClassifier(hidden_layer_sizes=(500, 250, 125), random_state=42, max_iter=3000)
        
        ensemble_model = VotingClassifier([('rf', rf_ens), ('gb', gb_ens), ('et', et_ens), ('mlp', mlp_ens)], voting='soft')
        ensemble_model.fit(X_ensemble_scaled, self.backtest_data['target'].values)
        
        y_pred_ensemble = ensemble_model.predict_proba(X_ensemble_scaled)[:, 1]
        signals_ensemble = np.where(y_pred_ensemble > np.percentile(y_pred_ensemble, 95), 1, 
                                   np.where(y_pred_ensemble < np.percentile(y_pred_ensemble, 5), -1, 0))
        
        self.backtest_data['ensemble_signal'] = signals_ensemble
        self.models['ensemble'] = {'model': ensemble_model, 'scaler': scaler_ensemble}

        return self

    def run_complete_analysis(self):
        """运行完整分析"""
        print("Starting Complete Convertible Bond Timing Analysis...")

        # 加载和处理数据
        self.load_and_process_data()

        # 构建策略
        self.build_strategies()
        self.build_ensemble_strategy()

        # 计算策略表现
        strategies = {
            'Model Deviation': 'deviation_signal',
            'Implied Volatility': 'volatility_signal',
            'Technical Factor': 'technical_signal',
            'Ensemble': 'ensemble_signal'
        }

        all_data = {}
        all_performance = {}

        for strategy_name, signal_col in strategies.items():
            data, performance = self.calculate_performance(signal_col, strategy_name)
            all_data[strategy_name] = data
            all_performance[strategy_name] = performance

            # 绘制单个策略图
            self.plot_strategy_performance(data, performance, strategy_name)

            print(f"\n{strategy_name} Performance:")
            print(f"Annual Return: {performance['Annual_Return']:.2%}")
            print(f"Total Return: {performance['Total_Return']:.2%}")
            print(f"Max Drawdown: {performance['Max_Drawdown']:.2%}")
            print(f"Sharpe Ratio: {performance['Sharpe_Ratio']:.3f}")
            print(f"Win Rate: {performance['Win_Rate']:.2%}")

        # 添加基准数据
        benchmark_data = self.backtest_data.copy()
        benchmark_data['benchmark_nav'] = (1 + benchmark_data['return']).cumprod()
        all_data['Benchmark'] = benchmark_data

        # 绘制所有策略对比图
        self.plot_all_strategies_comparison(all_data)

        # 导出结果
        self.export_results(all_data, all_performance)

        return all_data, all_performance

    def calculate_performance(self, signal_col, strategy_name, leverage=3.0):
        """计算策略表现"""
        data = self.backtest_data.copy()

        # 使用杠杆增强收益
        data['position'] = data[signal_col].shift(1).fillna(0) * leverage
        data['strategy_return'] = data['position'] * data['return']
        data['benchmark_return'] = data['return']

        # 计算累计净值
        data['strategy_nav'] = (1 + data['strategy_return']).cumprod()
        data['benchmark_nav'] = (1 + data['benchmark_return']).cumprod()

        # 计算买卖信号点
        data['buy_signal'] = (data[signal_col] == 1) & (data[signal_col].shift(1) != 1)
        data['sell_signal'] = (data[signal_col] == -1) & (data[signal_col].shift(1) != -1)

        # 计算性能指标
        total_return = data['strategy_nav'].iloc[-1] - 1
        years = len(data) / 252
        annual_return = (1 + total_return) ** (1/years) - 1
        annual_vol = data['strategy_return'].std() * np.sqrt(252)

        # 最大回撤
        peak = data['strategy_nav'].expanding().max()
        drawdown = (data['strategy_nav'] - peak) / peak
        max_drawdown = drawdown.min()
        max_dd_end = drawdown.idxmin()
        max_dd_start = data.loc[:max_dd_end, 'strategy_nav'].idxmax()

        # 其他指标
        sharpe_ratio = (annual_return - 0.02) / annual_vol if annual_vol > 0 else 0
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0

        winning_trades = (data['strategy_return'] > 0).sum()
        total_trades = (data['strategy_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        signal_changes = (data[signal_col] != data[signal_col].shift(1)).sum()

        performance = {
            'Strategy': strategy_name,
            'Annual_Return': annual_return,
            'Annual_Volatility': annual_vol,
            'Max_Drawdown': max_drawdown,
            'Sharpe_Ratio': sharpe_ratio,
            'Calmar_Ratio': calmar_ratio,
            'Max_DD_Start': max_dd_start,
            'Max_DD_End': max_dd_end,
            'Win_Rate': win_rate,
            'Signal_Count': signal_changes,
            'Total_Return': total_return
        }

        return data, performance
