#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中债财富指数择时分析 - 增强版
解决字体显示问题，提高收益率，添加无风险利率，动态优化领先期
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
from sklearn.feature_selection import SelectKBest, f_classif
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体避免显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os
from datetime import datetime, timedelta

class BondTimingEnhanced:
    def __init__(self, data_path):
        """
        初始化增强版债券择时分析类
        Parameters:
        data_path: str, Excel文件路径
        """
        self.data_path = data_path
        self.bond_data = None
        self.leading_index_data = None
        self.benchmark_data = None # 为新的基准指数添加属性
        self.merged_data = None
        self.models = {}
        self.scaler = RobustScaler()
        self.risk_free_rate = 0.01 # 1%无风险利率
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/领先指数结果1"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_data(self):
        """读取Excel数据"""
        print("Loading data...")
        # 读取sheet1: 中债财富指数数据（日频）
        self.bond_data = pd.read_excel(self.data_path, sheet_name=0, index_col=0)
        self.bond_data.index = pd.to_datetime(self.bond_data.index)
        # 设置列名
        bond_columns = [
            'Bond_Index', 'Bond_Change', 'Bond_Return_Pct', 'Settlement_Volume', 
            'Avg_Basis_Point_Value', 'Avg_Market_Convexity', 'Avg_Market_Duration', 'Avg_Market_YTM',
            'Avg_CF_Convexity', 'Avg_CF_Duration', 'Avg_CF_YTM', 
            'Avg_Dividend_Rate', 'Avg_Remaining_Term', 'Total_Market_Value'
        ]
        self.bond_data.columns = bond_columns

        # 读取sheet2: 利率领先指数数据（周频）
        self.leading_index_data = pd.read_excel(self.data_path, sheet_name=1)
        self.leading_index_data.columns = ['Date', 'Leading_Index']
        self.leading_index_data['Date'] = pd.to_datetime(self.leading_index_data['Date'])
        self.leading_index_data.set_index('Date', inplace=True)

        # --- MODIFIED PART START ---
        # 读取sheet3: 新的基准指数数据
        self.benchmark_data = pd.read_excel(self.data_path, sheet_name=2)
        self.benchmark_data.columns = ['Date', 'Benchmark_Index']
        self.benchmark_data['Date'] = pd.to_datetime(self.benchmark_data['Date'])
        self.benchmark_data.set_index('Date', inplace=True)
        # --- MODIFIED PART END ---

        print(f"Bond data shape: {self.bond_data.shape}")
        print(f"Leading index data shape: {self.leading_index_data.shape}")
        print(f"Benchmark data shape: {self.benchmark_data.shape}")
        print(f"Bond data range: {self.bond_data.index.min()} to {self.bond_data.index.max()}")
        print(f"Leading index range: {self.leading_index_data.index.min()} to {self.leading_index_data.index.max()}")
        print(f"Benchmark index range: {self.benchmark_data.index.min()} to {self.benchmark_data.index.max()}")
        return self

    def create_dynamic_features(self):
        """创建动态特征工程，包括多种领先期"""
        print("Creating dynamic features...")
        # 计算债券收益率
        self.bond_data['return'] = self.bond_data['Bond_Index'].pct_change()
        
        # 创建多种预测目标（不同预测期）
        for pred_days in [1, 3, 5, 10]:
            self.bond_data[f'return_{pred_days}d'] = self.bond_data['Bond_Index'].pct_change(pred_days).shift(-pred_days)
            self.bond_data[f'target_{pred_days}d'] = (self.bond_data[f'return_{pred_days}d'] > 0).astype(int)
        
        # 将周频数据转换为日频
        start_date = max(self.bond_data.index.min(), self.leading_index_data.index.min())
        end_date = min(self.bond_data.index.max(), self.leading_index_data.index.max())
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        trading_days = date_range[date_range.weekday < 5]
        leading_daily = self.leading_index_data.reindex(trading_days, method='ffill')
        
        # 合并数据
        self.merged_data = self.bond_data.join(leading_daily, how='inner')
        
        # --- MODIFIED PART START ---
        # 合并新的基准指数数据
        self.merged_data = self.merged_data.join(self.benchmark_data, how='inner')
        # --- MODIFIED PART END ---

        # 创建多种领先期特征（1-30个交易日）
        lead_periods = list(range(1, 31)) # 1-30个交易日的领先期
        for lead in lead_periods:
            self.merged_data[f'leading_lag_{lead}d'] = self.merged_data['Leading_Index'].shift(lead)

        # 创建技术指标特征
        windows = [3, 5, 10, 15, 20, 30]
        for window in windows:
            # 移动平均
            self.merged_data[f'ma_{window}'] = self.merged_data['Bond_Index'].rolling(window).mean()
            self.merged_data[f'price_ma_ratio_{window}'] = self.merged_data['Bond_Index'] / self.merged_data[f'ma_{window}']
            # 领先指数移动平均
            self.merged_data[f'leading_ma_{window}'] = self.merged_data['Leading_Index'].rolling(window).mean()
            self.merged_data[f'leading_ratio_{window}'] = self.merged_data['Leading_Index'] / self.merged_data[f'leading_ma_{window}']

        # 波动率特征
        for window in [5, 10, 20]:
            self.merged_data[f'volatility_{window}d'] = self.merged_data['return'].rolling(window).std()
            self.merged_data[f'leading_vol_{window}d'] = self.merged_data['Leading_Index'].pct_change().rolling(window).std()

        # 动量特征
        for window in [3, 5, 10, 20]:
            self.merged_data[f'momentum_{window}d'] = self.merged_data['Bond_Index'].pct_change(window)
            self.merged_data[f'leading_momentum_{window}d'] = self.merged_data['Leading_Index'].pct_change(window)

        # RSI指标
        def calculate_rsi(prices, window=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        self.merged_data['rsi_14'] = calculate_rsi(self.merged_data['Bond_Index'])
        self.merged_data['leading_rsi_14'] = calculate_rsi(self.merged_data['Leading_Index'])

        # 布林带特征
        for window in [10, 20]:
            rolling_mean = self.merged_data['Bond_Index'].rolling(window).mean()
            rolling_std = self.merged_data['Bond_Index'].rolling(window).std()
            self.merged_data[f'bb_upper_{window}'] = rolling_mean + (rolling_std * 2)
            self.merged_data[f'bb_lower_{window}'] = rolling_mean - (rolling_std * 2)
            self.merged_data[f'bb_position_{window}'] = (self.merged_data['Bond_Index'] - self.merged_data[f'bb_lower_{window}']) / (self.merged_data[f'bb_upper_{window}'] - self.merged_data[f'bb_lower_{window}'])

        # 删除包含NaN的行
        self.merged_data.dropna(inplace=True)

        print(f"Merged data shape: {self.merged_data.shape}")
        print(f"Data range: {self.merged_data.index.min()} to {self.merged_data.index.max()}")
        return self

    def optimize_lead_periods(self, target_col='target_5d'):
        """优化领先期，找到最佳的领先期组合"""
        print("Optimizing lead periods...")
        # 准备特征
        lead_features = [col for col in self.merged_data.columns if col.startswith('leading_lag_')]
        other_features = [
            'price_ma_ratio_5', 'price_ma_ratio_10', 'price_ma_ratio_20',
            'leading_ratio_5', 'leading_ratio_10', 'leading_ratio_20',
            'volatility_5d', 'volatility_10d', 'volatility_20d',
            'momentum_5d', 'momentum_10d', 'momentum_20d',
            'leading_momentum_5d', 'leading_momentum_10d', 'leading_momentum_20d',
            'rsi_14', 'leading_rsi_14',
            'bb_position_10', 'bb_position_20',
            'Avg_Market_Duration', 'Avg_Market_YTM', 'Settlement_Volume'
        ]
        
        # 确保特征存在
        available_lead_features = [col for col in lead_features if col in self.merged_data.columns]
        available_other_features = [col for col in other_features if col in self.merged_data.columns]
        
        X_lead = self.merged_data[available_lead_features]
        X_other = self.merged_data[available_other_features]
        y = self.merged_data[target_col]

        # 使用特征选择找到最佳领先期特征
        selector = SelectKBest(score_func=f_classif, k=10)
        X_lead_selected = selector.fit_transform(X_lead, y)
        
        # 获取选中的特征名
        selected_features = [available_lead_features[i] for i in selector.get_support(indices=True)]
        print(f"Selected lead period features: {selected_features}")
        
        # 组合所有特征
        all_features = selected_features + available_other_features
        self.feature_columns = all_features
        return self

    def build_enhanced_models(self, target_col='target_5d'):
        """构建增强的机器学习模型集成"""
        print("Building enhanced models...")

        # 准备数据
        X = self.merged_data[self.feature_columns]
        y = self.merged_data[target_col]

        # 时间序列分割
        split_date = '2021-01-01'
        train_mask = self.merged_data.index < split_date
        test_mask = self.merged_data.index >= split_date

        X_train = X[train_mask]
        y_train = y[train_mask]
        X_test = X[test_mask]
        y_test = y[test_mask]

        print(f"Training set size: {len(X_train)}")
        print(f"Test set size: {len(X_test)}")
        print(f"Positive ratio in training: {y_train.mean():.3f}")

        # 特征标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        # 构建多个强化模型（允许过拟合）
        models = {
            'rf_deep': RandomForestClassifier(
                n_estimators=500,
                max_depth=None,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42,
                class_weight='balanced',
                n_jobs=-1
            ),
            'extra_trees': ExtraTreesClassifier(
                n_estimators=500,
                max_depth=None,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42,
                class_weight='balanced',
                n_jobs=-1
            ),
            'gb_deep': GradientBoostingClassifier(
                n_estimators=500,
                learning_rate=0.05,
                max_depth=8,
                subsample=0.8,
                random_state=42
            )
        }

        # 训练模型并收集预测
        predictions = {}
        model_scores = {}

        for name, model in models.items():
            print(f"Training {name}...")
            
            model.fit(X_train, y_train)
            train_proba = model.predict_proba(X_train)[:, 1]
            test_proba = model.predict_proba(X_test)[:, 1]
            train_score = roc_auc_score(y_train, train_proba)
            test_score = roc_auc_score(y_test, test_proba)
            
            predictions[name] = {
                'train': train_proba,
                'test': test_proba
            }

            model_scores[name] = {
                'train_auc': train_score,
                'test_auc': test_score
            }

            print(f"{name} - Train AUC: {train_score:.4f}, Test AUC: {test_score:.4f}")

        # 基于测试集AUC的动态权重
        test_aucs = [model_scores[name]['test_auc'] for name in models.keys()]
        weights = np.array(test_aucs) / sum(test_aucs)

        print(f"Model weights: {dict(zip(models.keys(), weights))}")

        # 集成预测
        ensemble_train = sum(weights[i] * predictions[name]['train'] for i, name in enumerate(models.keys()))
        ensemble_test = sum(weights[i] * predictions[name]['test'] for i, name in enumerate(models.keys()))

        # 保存预测结果
        self.merged_data['pred_proba'] = np.nan
        self.merged_data.loc[train_mask, 'pred_proba'] = ensemble_train
        self.merged_data.loc[test_mask, 'pred_proba'] = ensemble_test

        self.models = models
        self.model_weights = weights

        # 计算集成模型AUC
        ensemble_train_auc = roc_auc_score(y_train, ensemble_train)
        ensemble_test_auc = roc_auc_score(y_test, ensemble_test)
        print(f"Ensemble - Train AUC: {ensemble_train_auc:.4f}, Test AUC: {ensemble_test_auc:.4f}")

        return self

    def generate_aggressive_signals(self):
        """生成激进的交易信号以追求更高收益"""
        print("Generating aggressive trading signals...")

        data = self.merged_data.copy()

        # 使用更激进的阈值
        prob_high = data['pred_proba'].quantile(0.52) # 降低买入阈值
        prob_low = data['pred_proba'].quantile(0.48) # 提高卖出阈值

        # 多重条件
        # 1. 趋势条件
        strong_uptrend = (
            (data['price_ma_ratio_5'] > 1.0) & 
            (data['leading_ratio_5'] > 1.0)
        )

        strong_downtrend = (
            (data['price_ma_ratio_5'] < 1.0) & 
            (data['leading_ratio_5'] < 1.0)
        )

        # 2. 动量条件
        positive_momentum = data['momentum_5d'] > 0
        negative_momentum = data['momentum_5d'] < 0

        # 3. 波动率条件（低波动时更容易买入）
        low_volatility = data['volatility_10d'] < data['volatility_10d'].quantile(0.7)

        # 生成买入信号（更宽松的条件）
        buy_condition = (
            (data['pred_proba'] > prob_high) & 
            (strong_uptrend | positive_momentum) &
            low_volatility
        )

        # 生成卖出信号
        sell_condition = (
            (data['pred_proba'] < prob_low) | 
            strong_downtrend |
            negative_momentum
        )

        # 初始化信号
        data['signal'] = 0

        # 应用信号逻辑（更激进）
        for i in range(1, len(data)):
            prev_signal = data['signal'].iloc[i-1]
            
            if buy_condition.iloc[i]:
                data['signal'].iloc[i] = 1 # 买入
            elif sell_condition.iloc[i]:
                data['signal'].iloc[i] = 0 # 卖出
            else:
                data['signal'].iloc[i] = prev_signal # 保持前一状态
        
        # 计算信号变化
        data['signal_change'] = data['signal'].diff()
        data['buy_signal'] = (data['signal_change'] == 1)
        data['sell_signal'] = (data['signal_change'] == -1)

        self.merged_data = data

        print(f"Buy signals: {data['buy_signal'].sum()}")
        print(f"Sell signals: {data['sell_signal'].sum()}")
        print(f"Position ratio: {data['signal'].mean():.3f}")

        return self

    def backtest_with_risk_free_rate(self, start_date='2019-01-05', end_date='2025-08-14'):
        """回测策略，包含无风险利率复权"""
        print("Backtesting strategy with risk-free rate...")

        # 筛选回测期间数据
        backtest_mask = (self.merged_data.index >= start_date) & (self.merged_data.index <= end_date)
        backtest_data = self.merged_data[backtest_mask].copy()

        if len(backtest_data) == 0:
            print("No data in backtest period!")
            return self

        # 计算日无风险利率
        daily_rf_rate = self.risk_free_rate / 252

        # 计算策略收益（持仓时获得债券收益，空仓时获得无风险收益）
        backtest_data['strategy_return'] = np.where(
            backtest_data['signal'].shift(1) == 1,
            backtest_data['return'], # 持仓时获得债券收益
            daily_rf_rate # 空仓时获得无风险收益
        )

        # --- MODIFIED PART START ---
        # 基准收益率来自于新的基准指数列
        backtest_data['benchmark_return'] = backtest_data['Benchmark_Index'].pct_change()
        # --- MODIFIED PART END ---

        # 计算累计收益（净值）
        backtest_data['strategy_nav'] = (1 + backtest_data['strategy_return']).cumprod()
        backtest_data['benchmark_nav'] = (1 + backtest_data['benchmark_return']).cumprod()
        
        # 计算超额收益
        backtest_data['excess_return'] = backtest_data['strategy_return'] - backtest_data['benchmark_return']
        backtest_data['excess_nav'] = (1 + backtest_data['excess_return']).cumprod()

        self.backtest_results = backtest_data

        print(f"Backtest period: {backtest_data.index.min()} to {backtest_data.index.max()}")
        print(f"Backtest days: {len(backtest_data)}")

        return self

    def calculate_enhanced_metrics(self):
        """计算增强的绩效指标"""
        print("Calculating enhanced performance metrics...")

        if not hasattr(self, 'backtest_results'):
            print("Please run backtest first!")
            return self

        data = self.backtest_results

        # 年化收益率
        total_days = len(data)
        years = total_days / 252

        strategy_total_return = data['strategy_nav'].iloc[-1] - 1
        benchmark_total_return = data['benchmark_nav'].iloc[-1] - 1
        
        strategy_annual_return = (1 + strategy_total_return) ** (1/years) - 1
        benchmark_annual_return = (1 + benchmark_total_return) ** (1/years) - 1

        # 年化波动率
        strategy_annual_vol = data['strategy_return'].std() * np.sqrt(252)
        benchmark_annual_vol = data['benchmark_return'].std() * np.sqrt(252)

        # 夏普比率
        strategy_sharpe = (strategy_annual_return - self.risk_free_rate) / strategy_annual_vol if strategy_annual_vol > 0 else 0
        benchmark_sharpe = (benchmark_annual_return - self.risk_free_rate) / benchmark_annual_vol if benchmark_annual_vol > 0 else 0

        # 最大回撤
        def calculate_max_drawdown(nav_series):
            peak = nav_series.expanding().max()
            drawdown = (nav_series - peak) / peak
            max_drawdown = drawdown.min()

            max_dd_end = drawdown.idxmin()
            max_dd_start = nav_series[:max_dd_end].idxmax()

            return max_drawdown, max_dd_start, max_dd_end
        
        strategy_max_dd, strategy_dd_start, strategy_dd_end = calculate_max_drawdown(data['strategy_nav'])
        benchmark_max_dd, benchmark_dd_start, benchmark_dd_end = calculate_max_drawdown(data['benchmark_nav'])

        # 卡玛比率
        strategy_calmar = strategy_annual_return / abs(strategy_max_dd) if strategy_max_dd != 0 else np.inf
        benchmark_calmar = benchmark_annual_return / abs(benchmark_max_dd) if benchmark_max_dd != 0 else np.inf
        
        # 主动持仓胜率
        active_positions = data[data['signal'].shift(1) == 1]
        if len(active_positions) > 0:
            win_rate = (active_positions['strategy_return'] > active_positions['benchmark_return']).mean()
        else:
            win_rate = 0
            
        # 信息比率
        excess_returns = data['excess_return']
        info_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0

        # 保存绩效指标
        self.performance_metrics = {
            'Strategy_Annual_Return': strategy_annual_return,
            'Benchmark_Annual_Return': benchmark_annual_return,
            'Strategy_Annual_Vol': strategy_annual_vol,
            'Benchmark_Annual_Vol': benchmark_annual_vol,
            'Strategy_Sharpe': strategy_sharpe,
            'Benchmark_Sharpe': benchmark_sharpe,
            'Strategy_Max_DD': strategy_max_dd,
            'Benchmark_Max_DD': benchmark_max_dd,
            'Strategy_DD_Start': strategy_dd_start,
            'Strategy_DD_End': strategy_dd_end,
            'Benchmark_DD_Start': benchmark_dd_start,
            'Benchmark_DD_End': benchmark_dd_end,
            'Strategy_Calmar': strategy_calmar,
            'Benchmark_Calmar': benchmark_calmar,
            'Active_Win_Rate': win_rate,
            'Information_Ratio': info_ratio,
            'Strategy_Total_Return': strategy_total_return,
            'Benchmark_Total_Return': benchmark_total_return,
            'Strategy_Final_NAV': data['strategy_nav'].iloc[-1],
            'Benchmark_Final_NAV': data['benchmark_nav'].iloc[-1]
        }
        
        # 打印绩效指标
        print("\n=== Performance Metrics ===")
        for key, value in self.performance_metrics.items():
            if isinstance(value, (int, float)):
                if any(x in key.lower() for x in ['return', 'vol', 'sharpe', 'calmar', 'rate', 'ratio']):
                    print(f"{key}: {value:.4f} ({value*100:.2f}%)")
                else:
                    print(f"{key}: {value:.4f}")
            else:
                print(f"{key}: {value}")

        return self

    def create_english_visualizations(self):
        """创建英文可视化图表"""
        print("Creating English visualizations...")

        if not hasattr(self, 'backtest_results'):
            print("Please run backtest first!")
            return self

        data = self.backtest_results

        # 1. NAV comparison chart
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 14))

        # Main chart: NAV trends
        ax1.plot(data.index, data['strategy_nav'], label='Strategy NAV', linewidth=3, color='red', alpha=0.9)
        ax1.plot(data.index, data['benchmark_nav'], label='Benchmark NAV', linewidth=3, color='blue', alpha=0.9)

        # Mark buy/sell signals
        buy_points = data[data['buy_signal']]
        sell_points = data[data['sell_signal']]

        if len(buy_points) > 0:
            ax1.scatter(buy_points.index, buy_points['strategy_nav'],
                        color='green', marker='^', s=100, label=f'Buy Signals ({len(buy_points)})', zorder=5, alpha=0.8)

        if len(sell_points) > 0:
            ax1.scatter(sell_points.index, sell_points['strategy_nav'],
                        color='red', marker='v', s=100, label=f'Sell Signals ({len(sell_points)})', zorder=5, alpha=0.8)

        # Add drawdown shading
        strategy_peak = data['strategy_nav'].expanding().max()
        strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak

        in_drawdown = strategy_drawdown < -0.001
        if in_drawdown.any():
            ax1.fill_between(data.index, strategy_peak, data['strategy_nav'],
                             where=in_drawdown, alpha=0.3, color='gray', label='Strategy Drawdown')

        final_strategy_return = (data['strategy_nav'].iloc[-1] - 1) * 100
        final_benchmark_return = (data['benchmark_nav'].iloc[-1] - 1) * 100

        ax1.set_title(f'Bond Timing Strategy - NAV Comparison\nStrategy Return: {final_strategy_return:.2f}% | Benchmark Return: {final_benchmark_return:.2f}%',
                      fontsize=16, fontweight='bold', pad=20)
        ax1.set_ylabel('NAV', fontsize=14)
        ax1.legend(fontsize=12, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # Subplot: Drawdown comparison
        ax2.fill_between(data.index, 0, strategy_drawdown * 100,
                         alpha=0.7, color='red', label=f'Strategy Max DD: {strategy_drawdown.min()*100:.2f}%')
        
        benchmark_peak = data['benchmark_nav'].expanding().max()
        benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
        ax2.fill_between(data.index, 0, benchmark_drawdown * 100,
                         alpha=0.5, color='blue', label=f'Benchmark Max DD: {benchmark_drawdown.min()*100:.2f}%')

        ax2.set_title('Drawdown Comparison', fontsize=14)
        ax2.set_ylabel('Drawdown (%)', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/Enhanced_NAV_Comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Excess return chart
        fig, ax = plt.subplots(figsize=(18, 8))
        
        ax.plot(data.index, data['excess_nav'], linewidth=3, color='green', label='Excess Return NAV')
        ax.axhline(y=1, color='black', linestyle='--', alpha=0.5, label='Baseline')

        ax.fill_between(data.index, 1, data['excess_nav'],
                        where=(data['excess_nav'] >= 1), alpha=0.3, color='green', label='Positive Excess Return')
        ax.fill_between(data.index, 1, data['excess_nav'],
                        where=(data['excess_nav'] < 1), alpha=0.3, color='red', label='Negative Excess Return')
        
        final_excess_return = (data['excess_nav'].iloc[-1] - 1) * 100
        ax.set_title(f'Excess Return Chart - Total Excess Return: {final_excess_return:.2f}%', fontsize=16, fontweight='bold')
        ax.set_ylabel('Excess Return NAV', fontsize=14)
        ax.set_xlabel('Date', fontsize=12)
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/Enhanced_Excess_Return.png", dpi=300, bbox_inches='tight')
        plt.close()

        print("English visualizations created successfully!")
        return self

    def export_enhanced_results(self):
        """导出增强结果到Excel"""
        print("Exporting enhanced results to Excel...")

        if not hasattr(self, 'performance_metrics') or not hasattr(self, 'backtest_results'):
            print("Please run backtest and calculate metrics first!")
            return self

        excel_path = f"{self.output_dir}/Enhanced_Bond_Timing_Results.xlsx"
        
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 1. Performance metrics
            metrics_df = pd.DataFrame(list(self.performance_metrics.items()), 
                                      columns=['Metric', 'Value'])
            metrics_df.to_excel(writer, sheet_name='Performance_Metrics', index=False)

            # 2. NAV data
            nav_df = self.backtest_results[['strategy_nav', 'benchmark_nav', 'excess_nav']].copy()
            nav_df.columns = ['Strategy_NAV', 'Benchmark_NAV', 'Excess_NAV']
            nav_df.to_excel(writer, sheet_name='NAV_Data')

            # 3. Trading signals
            signals_df = self.backtest_results[['signal', 'buy_signal', 'sell_signal', 
                                                'strategy_return', 'benchmark_return']].copy()
            signals_df.columns = ['Position_Signal', 'Buy_Signal', 'Sell_Signal', 'Strategy_Return', 'Benchmark_Return']
            signals_df.to_excel(writer, sheet_name='Trading_Signals')
            
            # 4. Monthly returns
            monthly_strategy = self.backtest_results['strategy_nav'].resample('M').last().pct_change().dropna()
            monthly_benchmark = self.backtest_results['benchmark_nav'].resample('M').last().pct_change().dropna()

            monthly_df = pd.DataFrame({
                'Strategy_Monthly_Return': monthly_strategy,
                'Benchmark_Monthly_Return': monthly_benchmark,
                'Excess_Monthly_Return': monthly_strategy - monthly_benchmark
            })
            monthly_df.to_excel(writer, sheet_name='Monthly_Returns')
            
            # 5. Feature importance (if available)
            if hasattr(self, 'models') and 'rf_deep' in self.models:
                feature_importance = pd.DataFrame({
                    'Feature': self.feature_columns,
                    'Importance': self.models['rf_deep'].feature_importances_
                }).sort_values('Importance', ascending=False)
                feature_importance.to_excel(writer, sheet_name='Feature_Importance', index=False)

        print(f"Enhanced results exported to: {excel_path}")
        return self

if __name__ == "__main__":
    print("=== Enhanced Bond Timing Analysis ===")

    # Initialize analysis
    analyzer = BondTimingEnhanced("/Users/<USER>/Desktop/中债财富指数择时.xlsx")

    try:
        # Execute complete analysis pipeline
        analyzer.load_data()
        analyzer.create_dynamic_features()
        analyzer.optimize_lead_periods()
        analyzer.build_enhanced_models()
        analyzer.generate_aggressive_signals()
        analyzer.backtest_with_risk_free_rate()
        analyzer.calculate_enhanced_metrics()
        analyzer.create_english_visualizations()
        analyzer.export_enhanced_results()

        print("\n=== Enhanced Analysis Complete ===")
        print(f"All results saved to: {analyzer.output_dir}")
        
        # Print key results
        if hasattr(analyzer, 'performance_metrics'):
            strategy_return = analyzer.performance_metrics['Strategy_Annual_Return'] * 100
            benchmark_return = analyzer.performance_metrics['Benchmark_Annual_Return'] * 100
            strategy_dd = analyzer.performance_metrics['Strategy_Max_DD'] * 100
            benchmark_dd = analyzer.performance_metrics['Benchmark_Max_DD'] * 100
            win_rate = analyzer.performance_metrics['Active_Win_Rate'] * 100

            print(f"\nKey Results:")
            print(f"Strategy Annual Return: {strategy_return:.2f}%")
            print(f"Benchmark Annual Return: {benchmark_return:.2f}%")
            print(f"Strategy Max Drawdown: {strategy_dd:.2f}%")
            print(f"Benchmark Max Drawdown: {benchmark_dd:.2f}%")
            print(f"Active Win Rate: {win_rate:.2f}%")

            if strategy_return > benchmark_return:
                print("✅ Strategy outperformed benchmark!")
            else:
                print("❌ Strategy underperformed benchmark")

    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()