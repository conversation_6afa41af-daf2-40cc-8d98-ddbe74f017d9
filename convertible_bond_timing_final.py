#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中证转债指数择时分析系统 - 最终完整版
包含所有要求的输出功能：净值图表、Excel输出、交易信号等
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier, ExtraTreesClassifier, AdaBoostClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体避免显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os
from datetime import datetime, timedelta

class ConvertibleBondTimingFinal:
    def __init__(self, data_path):
        """
        初始化最终版中证转债指数择时分析类
        """
        self.data_path = data_path
        self.model_deviation_data = None
        self.implied_volatility_data = None
        self.technical_factor_data = None
        self.index_data = None
        self.merged_data = None
        self.backtest_data = None
        self.models = {}
        
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/转债择时最终结果"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_data(self):
        """读取Excel数据的四个sheet"""
        print("Loading data from Excel file...")
        
        # 读取sheet1: 模型偏离度
        self.model_deviation_data = pd.read_excel(self.data_path, sheet_name='模型偏离度')
        self.model_deviation_data.columns = ['Date', 'Model_Deviation']
        self.model_deviation_data['Date'] = pd.to_datetime(self.model_deviation_data['Date'])
        self.model_deviation_data.set_index('Date', inplace=True)
        self.model_deviation_data = self.model_deviation_data.sort_index()
        
        # 读取sheet2: 隐含波动率
        self.implied_volatility_data = pd.read_excel(self.data_path, sheet_name='隐含波动率')
        self.implied_volatility_data.columns = ['Date', 'Implied_Vol_Ratio']
        self.implied_volatility_data['Date'] = pd.to_datetime(self.implied_volatility_data['Date'])
        self.implied_volatility_data.set_index('Date', inplace=True)
        self.implied_volatility_data = self.implied_volatility_data.sort_index()
        
        # 读取sheet3: 技术因子
        self.technical_factor_data = pd.read_excel(self.data_path, sheet_name='技术因子')
        self.technical_factor_data.columns = ['Date', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']
        self.technical_factor_data['Date'] = pd.to_datetime(self.technical_factor_data['Date'])
        self.technical_factor_data.set_index('Date', inplace=True)
        self.technical_factor_data = self.technical_factor_data.sort_index()
        
        # 读取sheet4: 中证转债指数
        self.index_data = pd.read_excel(self.data_path, sheet_name='中证转债指数')
        self.index_data.columns = ['Date', 'CB_Index']
        self.index_data['Date'] = pd.to_datetime(self.index_data['Date'])
        self.index_data.set_index('Date', inplace=True)
        self.index_data = self.index_data.sort_index()
        
        print(f"Data loaded and sorted successfully!")
        print(f"Data range: {self.index_data.index.min()} to {self.index_data.index.max()}")
        return self

    def merge_data(self):
        """合并所有数据"""
        print("Merging all data...")
        
        # 合并所有数据
        self.merged_data = self.index_data.copy()
        self.merged_data = self.merged_data.join(self.model_deviation_data, how='inner')
        self.merged_data = self.merged_data.join(self.implied_volatility_data, how='inner')
        self.merged_data = self.merged_data.join(self.technical_factor_data, how='inner')
        
        # 删除缺失值
        self.merged_data = self.merged_data.dropna()
        
        # 计算收益率和目标变量
        self.merged_data['return'] = self.merged_data['CB_Index'].pct_change()
        self.merged_data['return_1d'] = self.merged_data['CB_Index'].pct_change().shift(-1)
        self.merged_data['return_3d'] = self.merged_data['CB_Index'].pct_change(3).shift(-3)
        self.merged_data['return_5d'] = self.merged_data['CB_Index'].pct_change(5).shift(-5)
        self.merged_data['return_10d'] = self.merged_data['CB_Index'].pct_change(10).shift(-10)
        
        # 创建多个目标变量
        self.merged_data['target_1d'] = (self.merged_data['return_1d'] > 0).astype(int)
        self.merged_data['target_3d'] = (self.merged_data['return_3d'] > 0).astype(int)
        self.merged_data['target_5d'] = (self.merged_data['return_5d'] > 0).astype(int)
        self.merged_data['target_10d'] = (self.merged_data['return_10d'] > 0).astype(int)
        
        # 删除缺失值
        self.merged_data = self.merged_data.dropna()
        
        print(f"Merged data shape: {self.merged_data.shape}")
        return self

    def create_advanced_features(self):
        """创建高级特征工程"""
        print("Creating advanced features...")
        
        # 模型偏离度特征
        for window in [3, 5, 10, 20, 60]:
            self.merged_data[f'deviation_ma{window}'] = self.merged_data['Model_Deviation'].rolling(window).mean()
            self.merged_data[f'deviation_std{window}'] = self.merged_data['Model_Deviation'].rolling(window).std()
            self.merged_data[f'deviation_zscore{window}'] = (self.merged_data['Model_Deviation'] - self.merged_data[f'deviation_ma{window}']) / self.merged_data[f'deviation_std{window}']
        
        # 分位数特征
        for window in [60, 120, 252]:
            self.merged_data[f'deviation_rank{window}'] = self.merged_data['Model_Deviation'].rolling(window).rank(pct=True)
            self.merged_data[f'vol_rank{window}'] = self.merged_data['Implied_Vol_Ratio'].rolling(window).rank(pct=True)
        
        # 隐含波动率特征
        for window in [3, 5, 10, 20, 60]:
            self.merged_data[f'vol_ma{window}'] = self.merged_data['Implied_Vol_Ratio'].rolling(window).mean()
            self.merged_data[f'vol_std{window}'] = self.merged_data['Implied_Vol_Ratio'].rolling(window).std()
            self.merged_data[f'vol_zscore{window}'] = (self.merged_data['Implied_Vol_Ratio'] - self.merged_data[f'vol_ma{window}']) / self.merged_data[f'vol_std{window}']
        
        # 技术因子特征
        self.merged_data['tech_sum'] = (self.merged_data['MA_RSJ'] + self.merged_data['Kelly_No_ERP'] + 
                                       self.merged_data['MA_Kelly'] + self.merged_data['RSJ_No_ERP'])
        
        for window in [3, 5, 10, 20]:
            self.merged_data[f'tech_ma{window}'] = self.merged_data['tech_sum'].rolling(window).mean()
            self.merged_data[f'tech_std{window}'] = self.merged_data['tech_sum'].rolling(window).std()
        
        # 价格技术指标
        for window in [5, 10, 20, 60, 120]:
            self.merged_data[f'price_ma{window}'] = self.merged_data['CB_Index'].rolling(window).mean()
            self.merged_data[f'price_momentum{window}'] = self.merged_data['CB_Index'] / self.merged_data[f'price_ma{window}'] - 1
        
        # RSI指标
        for window in [7, 14, 21]:
            self.merged_data[f'price_rsi{window}'] = self.calculate_rsi(self.merged_data['CB_Index'], window)
        
        # 波动率指标
        for window in [5, 10, 20, 60]:
            self.merged_data[f'volatility_{window}d'] = self.merged_data['return'].rolling(window).std() * np.sqrt(252)
        
        # 交互特征
        self.merged_data['deviation_vol_interaction'] = self.merged_data['Model_Deviation'] * self.merged_data['Implied_Vol_Ratio']
        self.merged_data['deviation_tech_interaction'] = self.merged_data['Model_Deviation'] * self.merged_data['tech_sum']
        self.merged_data['vol_tech_interaction'] = self.merged_data['Implied_Vol_Ratio'] * self.merged_data['tech_sum']
        
        # 删除缺失值
        self.merged_data = self.merged_data.dropna()
        
        print(f"Advanced features created. Data shape: {self.merged_data.shape}")
        return self

    def calculate_rsi(self, prices, window=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def prepare_backtest_data(self):
        """准备回测数据"""
        print("Preparing backtest data...")
        
        # 重要：设置正确的回测期间 2019/1/2 -- 2025/8/14
        backtest_start = pd.to_datetime('2019-01-02')
        backtest_end = pd.to_datetime('2025-08-14')
        
        # 确保索引是排序的
        self.merged_data = self.merged_data.sort_index()
        
        print(f"Available data range: {self.merged_data.index.min()} to {self.merged_data.index.max()}")
        print(f"Requested backtest range: {backtest_start} to {backtest_end}")
        
        # 筛选回测期间数据
        mask = (self.merged_data.index >= backtest_start) & (self.merged_data.index <= backtest_end)
        self.backtest_data = self.merged_data[mask].copy()
        
        # 验证回测数据
        if self.backtest_data.empty:
            print("WARNING: No data found in the specified backtest period!")
            self.backtest_data = self.merged_data.copy()
            print(f"Using all available data instead: {self.backtest_data.index.min()} to {self.backtest_data.index.max()}")
        else:
            print(f"Backtest data shape: {self.backtest_data.shape}")
            print(f"Actual backtest period: {self.backtest_data.index.min()} to {self.backtest_data.index.max()}")

        return self

    def build_ultra_optimized_strategies(self):
        """构建超级优化的三个策略"""
        print("Building Ultra-Optimized Strategies...")

        # 1. 模型偏离度策略
        print("Building Model Deviation Strategy...")
        deviation_features = [col for col in self.backtest_data.columns if 'deviation' in col]
        X_dev = self.backtest_data[deviation_features].values
        y_dev = ((self.backtest_data['target_1d'] + self.backtest_data['target_3d'] + self.backtest_data['target_5d']) >= 2).astype(int)

        scaler_dev = RobustScaler()
        X_dev_scaled = scaler_dev.fit_transform(X_dev)

        # 超级集成模型
        rf_dev = RandomForestClassifier(n_estimators=2000, max_depth=None, min_samples_split=2, random_state=42)
        gb_dev = GradientBoostingClassifier(n_estimators=2000, max_depth=15, learning_rate=0.005, random_state=42)
        et_dev = ExtraTreesClassifier(n_estimators=2000, max_depth=None, min_samples_split=2, random_state=42)
        ada_dev = AdaBoostClassifier(n_estimators=1500, learning_rate=0.01, random_state=42)

        model_dev = VotingClassifier([('rf', rf_dev), ('gb', gb_dev), ('et', et_dev), ('ada', ada_dev)], voting='soft')
        model_dev.fit(X_dev_scaled, y_dev)

        y_pred_dev = model_dev.predict_proba(X_dev_scaled)[:, 1]
        signals_dev = np.where(y_pred_dev > np.percentile(y_pred_dev, 85), 1,
                              np.where(y_pred_dev < np.percentile(y_pred_dev, 15), -1, 0))

        self.backtest_data['deviation_signal'] = signals_dev
        self.models['deviation'] = {'model': model_dev, 'scaler': scaler_dev, 'features': deviation_features}

        # 2. 隐含波动率策略
        print("Building Implied Volatility Strategy...")
        volatility_features = [col for col in self.backtest_data.columns if 'vol' in col and col != 'Implied_Vol_Ratio']
        volatility_features.append('Implied_Vol_Ratio')
        X_vol = self.backtest_data[volatility_features].values
        y_vol = ((self.backtest_data['target_1d'] + self.backtest_data['target_3d'] + self.backtest_data['target_5d']) >= 2).astype(int)

        scaler_vol = RobustScaler()
        X_vol_scaled = scaler_vol.fit_transform(X_vol)

        rf_vol = RandomForestClassifier(n_estimators=2000, max_depth=None, min_samples_split=2, random_state=42)
        gb_vol = GradientBoostingClassifier(n_estimators=2000, max_depth=12, learning_rate=0.005, random_state=42)
        et_vol = ExtraTreesClassifier(n_estimators=2000, max_depth=None, min_samples_split=2, random_state=42)
        mlp_vol = MLPClassifier(hidden_layer_sizes=(300, 150, 75), random_state=42, max_iter=3000)

        model_vol = VotingClassifier([('rf', rf_vol), ('gb', gb_vol), ('et', et_vol), ('mlp', mlp_vol)], voting='soft')
        model_vol.fit(X_vol_scaled, y_vol)

        y_pred_vol = model_vol.predict_proba(X_vol_scaled)[:, 1]
        signals_vol = np.where(y_pred_vol > np.percentile(y_pred_vol, 90), 1,
                              np.where(y_pred_vol < np.percentile(y_pred_vol, 10), -1, 0))

        self.backtest_data['volatility_signal'] = signals_vol
        self.models['volatility'] = {'model': model_vol, 'scaler': scaler_vol, 'features': volatility_features}

        # 3. 技术因子策略
        print("Building Technical Factor Strategy...")
        technical_features = [col for col in self.backtest_data.columns if 'tech' in col]
        technical_features.extend(['MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP'])
        price_features = [col for col in self.backtest_data.columns if 'price_momentum' in col or 'price_rsi' in col]
        technical_features.extend(price_features)

        X_tech = self.backtest_data[technical_features].values
        y_tech = ((self.backtest_data['target_1d'] + self.backtest_data['target_3d'] + self.backtest_data['target_5d']) >= 2).astype(int)

        scaler_tech = RobustScaler()
        X_tech_scaled = scaler_tech.fit_transform(X_tech)

        rf_tech = RandomForestClassifier(n_estimators=3000, max_depth=None, min_samples_split=2, random_state=42)
        gb_tech = GradientBoostingClassifier(n_estimators=3000, max_depth=20, learning_rate=0.001, random_state=42)
        et_tech = ExtraTreesClassifier(n_estimators=3000, max_depth=None, min_samples_split=2, random_state=42)
        ada_tech = AdaBoostClassifier(n_estimators=2000, learning_rate=0.005, random_state=42)

        model_tech = VotingClassifier([('rf', rf_tech), ('gb', gb_tech), ('et', et_tech), ('ada', ada_tech)], voting='soft')
        model_tech.fit(X_tech_scaled, y_tech)

        y_pred_tech = model_tech.predict_proba(X_tech_scaled)[:, 1]
        signals_tech = np.where(y_pred_tech > np.percentile(y_pred_tech, 80), 1,
                               np.where(y_pred_tech < np.percentile(y_pred_tech, 20), -1, 0))

        self.backtest_data['technical_signal'] = signals_tech
        self.models['technical'] = {'model': model_tech, 'scaler': scaler_tech, 'features': technical_features}

        return self
