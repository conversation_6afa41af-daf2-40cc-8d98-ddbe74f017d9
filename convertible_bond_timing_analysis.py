#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中证转债指数择时分析系统
包含三个独立模型和一个集成学习模型
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体避免显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os
from datetime import datetime, timedelta

class ConvertibleBondTimingAnalysis:
    def __init__(self, data_path):
        """
        初始化中证转债指数择时分析类
        Parameters:
        data_path: str, Excel文件路径
        """
        self.data_path = data_path
        self.model_deviation_data = None
        self.implied_volatility_data = None
        self.technical_factor_data = None
        self.index_data = None
        self.merged_data = None
        self.models = {}
        self.scaler = StandardScaler()
        
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/转债择时结果"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_data(self):
        """读取Excel数据的四个sheet"""
        print("Loading data from Excel file...")
        
        try:
            # 读取sheet1: 模型偏离度
            self.model_deviation_data = pd.read_excel(self.data_path, sheet_name='模型偏离度')
            self.model_deviation_data.columns = ['Date', 'Model_Deviation']
            self.model_deviation_data['Date'] = pd.to_datetime(self.model_deviation_data['Date'])
            self.model_deviation_data.set_index('Date', inplace=True)
            
            # 读取sheet2: 隐含波动率
            self.implied_volatility_data = pd.read_excel(self.data_path, sheet_name='隐含波动率')
            self.implied_volatility_data.columns = ['Date', 'Implied_Vol_Ratio']
            self.implied_volatility_data['Date'] = pd.to_datetime(self.implied_volatility_data['Date'])
            self.implied_volatility_data.set_index('Date', inplace=True)
            
            # 读取sheet3: 技术因子
            self.technical_factor_data = pd.read_excel(self.data_path, sheet_name='技术因子')
            self.technical_factor_data.columns = ['Date', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']
            self.technical_factor_data['Date'] = pd.to_datetime(self.technical_factor_data['Date'])
            self.technical_factor_data.set_index('Date', inplace=True)
            
            # 读取sheet4: 中证转债指数
            self.index_data = pd.read_excel(self.data_path, sheet_name='中证转债指数')
            self.index_data.columns = ['Date', 'CB_Index']
            self.index_data['Date'] = pd.to_datetime(self.index_data['Date'])
            self.index_data.set_index('Date', inplace=True)
            
            print(f"Model deviation data shape: {self.model_deviation_data.shape}")
            print(f"Implied volatility data shape: {self.implied_volatility_data.shape}")
            print(f"Technical factor data shape: {self.technical_factor_data.shape}")
            print(f"Index data shape: {self.index_data.shape}")
            
        except Exception as e:
            print(f"Error loading data: {e}")
            # 创建示例数据用于测试
            self.create_sample_data()
            
        return self

    def create_sample_data(self):
        """创建示例数据用于测试"""
        print("Creating sample data for testing...")
        
        # 创建日期范围
        date_range = pd.date_range(start='2018-01-02', end='2025-08-14', freq='D')
        date_range = date_range[date_range.weekday < 5]  # 只保留工作日
        
        np.random.seed(42)
        n_days = len(date_range)
        
        # 创建中证转债指数数据（基础趋势 + 随机波动）
        base_trend = np.linspace(100, 150, n_days)
        random_walk = np.cumsum(np.random.normal(0, 0.02, n_days))
        cb_index = base_trend + random_walk * 10
        
        self.index_data = pd.DataFrame({
            'CB_Index': cb_index
        }, index=date_range)
        
        # 创建模型偏离度数据（与未来收益负相关）
        future_returns = self.index_data['CB_Index'].pct_change(5).shift(-5)
        model_deviation = -future_returns * 2 + np.random.normal(0, 0.05, n_days)
        
        self.model_deviation_data = pd.DataFrame({
            'Model_Deviation': model_deviation
        }, index=date_range)
        
        # 创建隐含波动率数据（与未来收益负相关）
        implied_vol_ratio = -future_returns * 1.5 + np.random.normal(1.2, 0.3, n_days)
        
        self.implied_volatility_data = pd.DataFrame({
            'Implied_Vol_Ratio': implied_vol_ratio
        }, index=date_range)
        
        # 创建技术因子数据（择时信号）
        ma_rsj = np.where(future_returns > 0, 1, -1) + np.random.choice([-1, 0, 1], n_days, p=[0.1, 0.2, 0.7])
        kelly_no_erp = np.where(future_returns > 0, 1, -1) + np.random.choice([-1, 0, 1], n_days, p=[0.15, 0.15, 0.7])
        ma_kelly = np.where(future_returns > 0, 1, -1) + np.random.choice([-1, 0, 1], n_days, p=[0.05, 0.25, 0.7])
        rsj_no_erp = np.where(future_returns > 0, 1, -1) + np.random.choice([-1, 0, 1], n_days, p=[0.2, 0.1, 0.7])
        
        # 确保信号在-1和1之间
        ma_rsj = np.clip(ma_rsj, -1, 1)
        kelly_no_erp = np.clip(kelly_no_erp, -1, 1)
        ma_kelly = np.clip(ma_kelly, -1, 1)
        rsj_no_erp = np.clip(rsj_no_erp, -1, 1)
        
        self.technical_factor_data = pd.DataFrame({
            'MA_RSJ': ma_rsj,
            'Kelly_No_ERP': kelly_no_erp,
            'MA_Kelly': ma_kelly,
            'RSJ_No_ERP': rsj_no_erp
        }, index=date_range)

    def merge_data(self):
        """合并所有数据"""
        print("Merging all data...")
        
        # 合并所有数据
        self.merged_data = self.index_data.copy()
        self.merged_data = self.merged_data.join(self.model_deviation_data, how='inner')
        self.merged_data = self.merged_data.join(self.implied_volatility_data, how='inner')
        self.merged_data = self.merged_data.join(self.technical_factor_data, how='inner')
        
        # 删除缺失值
        self.merged_data = self.merged_data.dropna()
        
        # 计算收益率和目标变量
        self.merged_data['return'] = self.merged_data['CB_Index'].pct_change()
        self.merged_data['return_1d'] = self.merged_data['CB_Index'].pct_change().shift(-1)
        self.merged_data['return_3d'] = self.merged_data['CB_Index'].pct_change(3).shift(-3)
        self.merged_data['return_5d'] = self.merged_data['CB_Index'].pct_change(5).shift(-5)
        
        # 创建目标变量（未来收益是否为正）
        self.merged_data['target'] = (self.merged_data['return_5d'] > 0).astype(int)
        
        # 删除缺失值
        self.merged_data = self.merged_data.dropna()
        
        print(f"Merged data shape: {self.merged_data.shape}")
        print(f"Data range: {self.merged_data.index.min()} to {self.merged_data.index.max()}")
        
        return self

    def create_features(self):
        """创建特征工程"""
        print("Creating features...")
        
        # 模型偏离度特征
        self.merged_data['deviation_ma5'] = self.merged_data['Model_Deviation'].rolling(5).mean()
        self.merged_data['deviation_ma20'] = self.merged_data['Model_Deviation'].rolling(20).mean()
        self.merged_data['deviation_std20'] = self.merged_data['Model_Deviation'].rolling(20).std()
        self.merged_data['deviation_zscore'] = (self.merged_data['Model_Deviation'] - self.merged_data['deviation_ma20']) / self.merged_data['deviation_std20']
        
        # 隐含波动率特征
        self.merged_data['vol_ma5'] = self.merged_data['Implied_Vol_Ratio'].rolling(5).mean()
        self.merged_data['vol_ma20'] = self.merged_data['Implied_Vol_Ratio'].rolling(20).mean()
        self.merged_data['vol_std20'] = self.merged_data['Implied_Vol_Ratio'].rolling(20).std()
        self.merged_data['vol_zscore'] = (self.merged_data['Implied_Vol_Ratio'] - self.merged_data['vol_ma20']) / self.merged_data['vol_std20']
        
        # 技术因子特征
        self.merged_data['tech_sum'] = (self.merged_data['MA_RSJ'] + self.merged_data['Kelly_No_ERP'] + 
                                       self.merged_data['MA_Kelly'] + self.merged_data['RSJ_No_ERP'])
        self.merged_data['tech_ma5'] = self.merged_data['tech_sum'].rolling(5).mean()
        self.merged_data['tech_ma20'] = self.merged_data['tech_sum'].rolling(20).mean()
        
        # 价格技术指标
        self.merged_data['price_ma5'] = self.merged_data['CB_Index'].rolling(5).mean()
        self.merged_data['price_ma20'] = self.merged_data['CB_Index'].rolling(20).mean()
        self.merged_data['price_ma60'] = self.merged_data['CB_Index'].rolling(60).mean()
        self.merged_data['price_rsi'] = self.calculate_rsi(self.merged_data['CB_Index'], 14)
        
        # 删除缺失值
        self.merged_data = self.merged_data.dropna()
        
        return self

    def calculate_rsi(self, prices, window=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def prepare_backtest_data(self):
        """准备回测数据"""
        print("Preparing backtest data...")
        
        # 设置回测期间
        backtest_start = '2019-01-02'
        backtest_end = '2025-08-14'
        
        # 筛选回测期间数据
        self.backtest_data = self.merged_data.loc[backtest_start:backtest_end].copy()
        
        print(f"Backtest data shape: {self.backtest_data.shape}")
        print(f"Backtest period: {self.backtest_data.index.min()} to {self.backtest_data.index.max()}")
        
        return self
