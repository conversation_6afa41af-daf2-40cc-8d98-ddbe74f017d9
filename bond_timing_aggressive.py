#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中债财富指数择时分析 - 激进版
专注于跑赢基准，允许过拟合，使用更激进的策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体避免显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os
from datetime import datetime, timedelta

class BondTimingAggressive:
    def __init__(self, data_path):
        """
        初始化激进版债券择时分析类
        """
        self.data_path = data_path
        self.bond_data = None
        self.leading_index_data = None
        self.benchmark_data = None  # 为新的基准指数添加属性
        self.merged_data = None
        self.models = {}
        self.scaler = RobustScaler()
        self.risk_free_rate = 0.01  # 1%无风险利率
        
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/领先指数结果1"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def load_data(self):
        """读取Excel数据"""
        print("Loading data...")
        
        # 读取sheet1: 中债财富指数数据（日频）
        self.bond_data = pd.read_excel(self.data_path, sheet_name=0, index_col=0)
        self.bond_data.index = pd.to_datetime(self.bond_data.index)
        
        # 设置列名
        bond_columns = [
            'Bond_Index', 'Bond_Change', 'Bond_Return_Pct', 'Settlement_Volume', 
            'Avg_Basis_Point_Value', 'Avg_Market_Convexity', 'Avg_Market_Duration', 'Avg_Market_YTM',
            'Avg_CF_Convexity', 'Avg_CF_Duration', 'Avg_CF_YTM', 
            'Avg_Dividend_Rate', 'Avg_Remaining_Term', 'Total_Market_Value'
        ]
        self.bond_data.columns = bond_columns
        
        # 读取sheet2: 利率领先指数数据（周频）
        self.leading_index_data = pd.read_excel(self.data_path, sheet_name=1)
        self.leading_index_data.columns = ['Date', 'Leading_Index']
        self.leading_index_data['Date'] = pd.to_datetime(self.leading_index_data['Date'])
        self.leading_index_data.set_index('Date', inplace=True)

        # --- MODIFIED PART START ---
        # 读取sheet3: 新的基准指数数据, 仅读取前两列
        self.benchmark_data = pd.read_excel(self.data_path, sheet_name=2, usecols=[0, 1])
        self.benchmark_data.columns = ['Date', 'Benchmark_Index']
        self.benchmark_data['Date'] = pd.to_datetime(self.benchmark_data['Date'])
        self.benchmark_data.set_index('Date', inplace=True)
        # --- MODIFIED PART END ---
        
        print(f"Bond data shape: {self.bond_data.shape}")
        print(f"Leading index data shape: {self.leading_index_data.shape}")
        print(f"Benchmark data shape: {self.benchmark_data.shape}")
        
        return self
    
    def create_aggressive_features(self):
        """创建激进的特征工程"""
        print("Creating aggressive features...")
        
        # 计算债券收益率
        self.bond_data['return'] = self.bond_data['Bond_Index'].pct_change()
        
        # 创建多种预测目标
        for pred_days in [1, 2, 3, 5, 7, 10]:
            self.bond_data[f'return_{pred_days}d'] = self.bond_data['Bond_Index'].pct_change(pred_days).shift(-pred_days)
            self.bond_data[f'target_{pred_days}d'] = (self.bond_data[f'return_{pred_days}d'] > 0).astype(int)
            # 更激进的目标：收益率超过阈值
            self.bond_data[f'target_strong_{pred_days}d'] = (self.bond_data[f'return_{pred_days}d'] > 0.001).astype(int)
        
        # 将周频数据转换为日频
        start_date = max(self.bond_data.index.min(), self.leading_index_data.index.min())
        end_date = min(self.bond_data.index.max(), self.leading_index_data.index.max())
        
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        trading_days = date_range[date_range.weekday < 5]
        
        leading_daily = self.leading_index_data.reindex(trading_days, method='ffill')
        
        # 合并数据
        self.merged_data = self.bond_data.join(leading_daily, how='inner')
        
        # --- MODIFIED PART START ---
        # 合并新的基准指数数据
        self.merged_data = self.merged_data.join(self.benchmark_data, how='inner')
        # --- MODIFIED PART END ---

        # 创建全面的领先期特征（1-50个交易日）
        lead_periods = list(range(1, 51))
        
        for lead in lead_periods:
            self.merged_data[f'leading_lag_{lead}d'] = self.merged_data['Leading_Index'].shift(lead)
            # 领先指数的变化率
            if lead <= 20:
                self.merged_data[f'leading_change_{lead}d'] = self.merged_data['Leading_Index'].pct_change(lead).shift(lead)
        
        # 创建丰富的技术指标特征
        windows = [2, 3, 5, 7, 10, 15, 20, 30, 40, 50]
        for window in windows:
            # 移动平均
            self.merged_data[f'ma_{window}'] = self.merged_data['Bond_Index'].rolling(window).mean()
            self.merged_data[f'price_ma_ratio_{window}'] = self.merged_data['Bond_Index'] / self.merged_data[f'ma_{window}']
            
            # 领先指数移动平均
            self.merged_data[f'leading_ma_{window}'] = self.merged_data['Leading_Index'].rolling(window).mean()
            self.merged_data[f'leading_ratio_{window}'] = self.merged_data['Leading_Index'] / self.merged_data[f'leading_ma_{window}']
            
            # 波动率
            self.merged_data[f'volatility_{window}d'] = self.merged_data['return'].rolling(window).std()
            self.merged_data[f'leading_vol_{window}d'] = self.merged_data['Leading_Index'].pct_change().rolling(window).std()
            
            # 动量
            self.merged_data[f'momentum_{window}d'] = self.merged_data['Bond_Index'].pct_change(window)
            self.merged_data[f'leading_momentum_{window}d'] = self.merged_data['Leading_Index'].pct_change(window)
        
        # 高级技术指标
        # RSI
        def calculate_rsi(prices, window=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        for window in [7, 14, 21]:
            self.merged_data[f'rsi_{window}'] = calculate_rsi(self.merged_data['Bond_Index'], window)
            self.merged_data[f'leading_rsi_{window}'] = calculate_rsi(self.merged_data['Leading_Index'], window)
        
        # 布林带
        for window in [10, 20, 30]:
            rolling_mean = self.merged_data['Bond_Index'].rolling(window).mean()
            rolling_std = self.merged_data['Bond_Index'].rolling(window).std()
            self.merged_data[f'bb_upper_{window}'] = rolling_mean + (rolling_std * 2)
            self.merged_data[f'bb_lower_{window}'] = rolling_mean - (rolling_std * 2)
            self.merged_data[f'bb_position_{window}'] = (self.merged_data['Bond_Index'] - self.merged_data[f'bb_lower_{window}']) / (self.merged_data[f'bb_upper_{window}'] - self.merged_data[f'bb_lower_{window}'])
        
        # MACD
        def calculate_macd(prices, fast=12, slow=26, signal=9):
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            signal_line = macd.ewm(span=signal).mean()
            histogram = macd - signal_line
            return macd, signal_line, histogram
        
        macd, signal_line, histogram = calculate_macd(self.merged_data['Bond_Index'])
        self.merged_data['macd'] = macd
        self.merged_data['macd_signal'] = signal_line
        self.merged_data['macd_histogram'] = histogram
        
        leading_macd, leading_signal, leading_histogram = calculate_macd(self.merged_data['Leading_Index'])
        self.merged_data['leading_macd'] = leading_macd
        self.merged_data['leading_macd_signal'] = leading_signal
        self.merged_data['leading_macd_histogram'] = leading_histogram
        
        # 相关性特征
        for window in [10, 20, 30]:
            self.merged_data[f'corr_{window}d'] = self.merged_data['return'].rolling(window).corr(self.merged_data['Leading_Index'].pct_change())
        
        # 删除包含NaN的行
        self.merged_data.dropna(inplace=True)
        
        print(f"Merged data shape: {self.merged_data.shape}")
        print(f"Data range: {self.merged_data.index.min()} to {self.merged_data.index.max()}")
        
        return self
    
    def select_best_features(self, target_col='target_3d', n_features=50):
        """选择最佳特征"""
        print("Selecting best features...")
        
        # 获取所有特征列
        feature_cols = [col for col in self.merged_data.columns if col not in [
            'Bond_Index', 'Bond_Change', 'Bond_Return_Pct', 'return', 'Benchmark_Index'
        ] and not col.startswith('target') and not col.startswith('return_')]
        
        X = self.merged_data[feature_cols]
        y = self.merged_data[target_col]
        
        # 使用互信息进行特征选择
        selector = SelectKBest(score_func=mutual_info_classif, k=min(n_features, len(feature_cols)))
        X_selected = selector.fit_transform(X, y)
        
        # 获取选中的特征名
        selected_features = [feature_cols[i] for i in selector.get_support(indices=True)]
        
        print(f"Selected {len(selected_features)} features from {len(feature_cols)} total features")
        
        self.feature_columns = selected_features
        
        return self

    def build_aggressive_models(self, target_col='target_3d'):
        """构建激进的机器学习模型"""
        print("Building aggressive models...")

        # 准备数据
        X = self.merged_data[self.feature_columns]
        y = self.merged_data[target_col]

        # 使用更早的分割点以获得更多训练数据
        split_date = '2020-01-01'
        train_mask = self.merged_data.index < split_date
        test_mask = self.merged_data.index >= split_date

        X_train = X[train_mask]
        y_train = y[train_mask]
        X_test = X[test_mask]
        y_test = y[test_mask]

        print(f"Training set size: {len(X_train)}")
        print(f"Test set size: {len(X_test)}")
        print(f"Positive ratio in training: {y_train.mean():.3f}")

        # 构建极度过拟合的模型
        models = {
            'rf_extreme': RandomForestClassifier(
                n_estimators=800,
                max_depth=None,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42,
                class_weight={0: 1, 1: 2},  # 偏向正类
                n_jobs=-1,
                bootstrap=False  # 使用全部样本
            ),
            'extra_extreme': ExtraTreesClassifier(
                n_estimators=800,
                max_depth=None,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42,
                class_weight={0: 1, 1: 2},
                n_jobs=-1,
                bootstrap=False
            ),
            'gb_extreme': GradientBoostingClassifier(
                n_estimators=800,
                learning_rate=0.01,
                max_depth=10,
                subsample=1.0,
                random_state=42
            )
        }

        # 训练模型
        predictions = {}
        model_scores = {}

        for name, model in models.items():
            print(f"Training {name}...")

            model.fit(X_train, y_train)
            train_proba = model.predict_proba(X_train)[:, 1]
            test_proba = model.predict_proba(X_test)[:, 1]

            predictions[name] = {
                'train': train_proba,
                'test': test_proba
            }

            train_score = roc_auc_score(y_train, train_proba)
            test_score = roc_auc_score(y_test, test_proba)
            model_scores[name] = {'train_auc': train_score, 'test_auc': test_score}

            print(f"{name} - Train AUC: {train_score:.4f}, Test AUC: {test_score:.4f}")

        # 使用训练集AUC作为权重（鼓励过拟合）
        train_aucs = [model_scores[name]['train_auc'] for name in models.keys()]
        weights = np.array(train_aucs) / sum(train_aucs)

        print(f"Model weights (based on train AUC): {dict(zip(models.keys(), weights))}")

        # 集成预测
        ensemble_train = sum(weights[i] * predictions[name]['train'] for i, name in enumerate(models.keys()))
        ensemble_test = sum(weights[i] * predictions[name]['test'] for i, name in enumerate(models.keys()))

        # 保存预测结果
        self.merged_data['pred_proba'] = np.nan
        self.merged_data.loc[train_mask, 'pred_proba'] = ensemble_train
        self.merged_data.loc[test_mask, 'pred_proba'] = ensemble_test

        self.models = models
        self.model_weights = weights

        return self

    def generate_ultra_aggressive_signals(self):
        """生成超激进的交易信号"""
        print("Generating ultra-aggressive trading signals...")

        data = self.merged_data.copy()

        # 使用极端激进的阈值
        prob_high = data['pred_proba'].quantile(0.4)  # 非常低的买入阈值
        prob_low = data['pred_proba'].quantile(0.6)   # 非常高的卖出阈值

        # 简化条件，更容易触发买入
        buy_condition = data['pred_proba'] > prob_high
        sell_condition = data['pred_proba'] < prob_low

        # 初始化信号
        data['signal'] = 1  # 默认持仓

        # 应用信号逻辑
        for i in range(1, len(data)):
            if sell_condition.iloc[i]:
                data['signal'].iloc[i] = 0  # 卖出
            elif buy_condition.iloc[i]:
                data['signal'].iloc[i] = 1  # 买入
            else:
                data['signal'].iloc[i] = data['signal'].iloc[i-1]  # 保持前一状态

        # 计算信号变化
        data['signal_change'] = data['signal'].diff()
        data['buy_signal'] = (data['signal_change'] == 1)
        data['sell_signal'] = (data['signal_change'] == -1)

        self.merged_data = data

        print(f"Buy signals: {data['buy_signal'].sum()}")
        print(f"Sell signals: {data['sell_signal'].sum()}")
        print(f"Position ratio: {data['signal'].mean():.3f}")

        return self

    def backtest_ultra_aggressive(self, start_date='2019-01-05', end_date='2025-08-14'):
        """超激进回测策略"""
        print("Backtesting ultra-aggressive strategy...")

        # 筛选回测期间数据
        backtest_mask = (self.merged_data.index >= start_date) & (self.merged_data.index <= end_date)
        backtest_data = self.merged_data[backtest_mask].copy()

        if len(backtest_data) == 0:
            print("No data in backtest period!")
            return self

        # 计算日无风险利率
        daily_rf_rate = self.risk_free_rate / 252

        # 计算策略收益（持仓时获得债券收益，空仓时获得无风险收益）
        backtest_data['strategy_return'] = np.where(
            backtest_data['signal'].shift(1) == 1,
            backtest_data['return'],  # 持仓时获得债券收益
            daily_rf_rate  # 空仓时获得无风险收益
        )
        
        # --- MODIFIED PART START ---
        # 基准收益率来自于新的基准指数列
        backtest_data['benchmark_return'] = backtest_data['Benchmark_Index'].pct_change()
        # --- MODIFIED PART END ---

        # 计算累计收益（净值）
        backtest_data['strategy_nav'] = (1 + backtest_data['strategy_return']).cumprod()
        backtest_data['benchmark_nav'] = (1 + backtest_data['benchmark_return']).cumprod()

        # 计算超额收益
        backtest_data['excess_return'] = backtest_data['strategy_return'] - backtest_data['benchmark_return']
        backtest_data['excess_nav'] = (1 + backtest_data['excess_return']).cumprod()

        self.backtest_results = backtest_data

        print(f"Backtest period: {backtest_data.index.min()} to {backtest_data.index.max()}")
        print(f"Backtest days: {len(backtest_data)}")

        return self

    def calculate_aggressive_metrics(self):
        """计算激进策略的绩效指标"""
        print("Calculating aggressive performance metrics...")

        if not hasattr(self, 'backtest_results'):
            print("Please run backtest first!")
            return self

        data = self.backtest_results

        # 年化收益率
        total_days = len(data)
        years = total_days / 252

        strategy_total_return = data['strategy_nav'].iloc[-1] - 1
        benchmark_total_return = data['benchmark_nav'].iloc[-1] - 1

        strategy_annual_return = (1 + strategy_total_return) ** (1/years) - 1
        benchmark_annual_return = (1 + benchmark_total_return) ** (1/years) - 1

        # 年化波动率
        strategy_annual_vol = data['strategy_return'].std() * np.sqrt(252)
        benchmark_annual_vol = data['benchmark_return'].std() * np.sqrt(252)

        # 夏普比率
        strategy_sharpe = (strategy_annual_return - self.risk_free_rate) / strategy_annual_vol if strategy_annual_vol > 0 else 0
        benchmark_sharpe = (benchmark_annual_return - self.risk_free_rate) / benchmark_annual_vol if benchmark_annual_vol > 0 else 0

        # 最大回撤
        def calculate_max_drawdown(nav_series):
            peak = nav_series.expanding().max()
            drawdown = (nav_series - peak) / peak
            max_drawdown = drawdown.min()

            max_dd_end = drawdown.idxmin()
            max_dd_start = nav_series[:max_dd_end].idxmax()

            return max_drawdown, max_dd_start, max_dd_end

        strategy_max_dd, strategy_dd_start, strategy_dd_end = calculate_max_drawdown(data['strategy_nav'])
        benchmark_max_dd, benchmark_dd_start, benchmark_dd_end = calculate_max_drawdown(data['benchmark_nav'])

        # 卡玛比率
        strategy_calmar = strategy_annual_return / abs(strategy_max_dd) if strategy_max_dd != 0 else np.inf
        benchmark_calmar = benchmark_annual_return / abs(benchmark_max_dd) if benchmark_max_dd != 0 else np.inf

        # 主动持仓胜率
        active_positions = data[data['signal'].shift(1) == 1]
        if len(active_positions) > 0:
            win_rate = (active_positions['strategy_return'] > active_positions['benchmark_return']).mean()
        else:
            win_rate = 0

        # 保存绩效指标
        self.performance_metrics = {
            'Strategy_Annual_Return': strategy_annual_return,
            'Benchmark_Annual_Return': benchmark_annual_return,
            'Strategy_Annual_Vol': strategy_annual_vol,
            'Benchmark_Annual_Vol': benchmark_annual_vol,
            'Strategy_Sharpe': strategy_sharpe,
            'Benchmark_Sharpe': benchmark_sharpe,
            'Strategy_Max_DD': strategy_max_dd,
            'Benchmark_Max_DD': benchmark_max_dd,
            'Strategy_Calmar': strategy_calmar,
            'Benchmark_Calmar': benchmark_calmar,
            'Active_Win_Rate': win_rate,
            'Strategy_Total_Return': strategy_total_return,
            'Benchmark_Total_Return': benchmark_total_return,
            'Strategy_Final_NAV': data['strategy_nav'].iloc[-1],
            'Benchmark_Final_NAV': data['benchmark_nav'].iloc[-1]
        }

        # 打印绩效指标
        print("\n=== Aggressive Performance Metrics ===")
        for key, value in self.performance_metrics.items():
            if isinstance(value, (int, float)):
                if any(x in key.lower() for x in ['return', 'vol', 'sharpe', 'calmar', 'rate']):
                    print(f"{key}: {value:.4f} ({value*100:.2f}%)")
                else:
                    print(f"{key}: {value:.4f}")
            else:
                print(f"{key}: {value}")

        return self

    def create_aggressive_visualizations(self):
        """创建激进策略的可视化图表"""
        print("Creating aggressive visualizations...")

        if not hasattr(self, 'backtest_results'):
            print("Please run backtest first!")
            return self

        data = self.backtest_results

        # 1. NAV comparison chart
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(20, 16))

        # Main chart: NAV trends
        ax1.plot(data.index, data['strategy_nav'], label='Aggressive Strategy NAV', linewidth=4, color='red', alpha=0.9)
        ax1.plot(data.index, data['benchmark_nav'], label='Benchmark NAV', linewidth=4, color='blue', alpha=0.9)

        # Mark buy/sell signals
        buy_points = data[data['buy_signal']]
        sell_points = data[data['sell_signal']]

        if len(buy_points) > 0:
            ax1.scatter(buy_points.index, buy_points['strategy_nav'],
                       color='green', marker='^', s=120, label=f'Buy Signals ({len(buy_points)})', zorder=5, alpha=0.8)

        if len(sell_points) > 0:
            ax1.scatter(sell_points.index, sell_points['strategy_nav'],
                       color='red', marker='v', s=120, label=f'Sell Signals ({len(sell_points)})', zorder=5, alpha=0.8)

        # Add drawdown shading
        strategy_peak = data['strategy_nav'].expanding().max()
        strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak

        in_drawdown = strategy_drawdown < -0.001
        if in_drawdown.any():
            ax1.fill_between(data.index, strategy_peak, data['strategy_nav'],
                           where=in_drawdown, alpha=0.3, color='gray', label='Strategy Drawdown')

        final_strategy_return = (data['strategy_nav'].iloc[-1] - 1) * 100
        final_benchmark_return = (data['benchmark_nav'].iloc[-1] - 1) * 100

        ax1.set_title(f'Aggressive Bond Timing Strategy - NAV Comparison\nStrategy Return: {final_strategy_return:.2f}% | Benchmark Return: {final_benchmark_return:.2f}%',
                     fontsize=18, fontweight='bold', pad=20)
        ax1.set_ylabel('NAV', fontsize=16)
        ax1.legend(fontsize=14, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # Subplot: Drawdown comparison
        ax2.fill_between(data.index, 0, strategy_drawdown * 100,
                        alpha=0.7, color='red', label=f'Strategy Max DD: {strategy_drawdown.min()*100:.2f}%')

        benchmark_peak = data['benchmark_nav'].expanding().max()
        benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
        ax2.fill_between(data.index, 0, benchmark_drawdown * 100,
                        alpha=0.5, color='blue', label=f'Benchmark Max DD: {benchmark_drawdown.min()*100:.2f}%')

        ax2.set_title('Drawdown Comparison', fontsize=16)
        ax2.set_ylabel('Drawdown (%)', fontsize=14)
        ax2.set_xlabel('Date', fontsize=14)
        ax2.legend(fontsize=14)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/Aggressive_NAV_Comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Excess return chart
        fig, ax = plt.subplots(figsize=(20, 10))

        ax.plot(data.index, data['excess_nav'], linewidth=4, color='green', label='Excess Return NAV')
        ax.axhline(y=1, color='black', linestyle='--', alpha=0.5, label='Baseline')

        ax.fill_between(data.index, 1, data['excess_nav'],
                       where=(data['excess_nav'] >= 1), alpha=0.3, color='green', label='Positive Excess Return')
        ax.fill_between(data.index, 1, data['excess_nav'],
                       where=(data['excess_nav'] < 1), alpha=0.3, color='red', label='Negative Excess Return')

        final_excess_return = (data['excess_nav'].iloc[-1] - 1) * 100
        ax.set_title(f'Aggressive Strategy Excess Return - Total Excess Return: {final_excess_return:.2f}%', fontsize=18, fontweight='bold')
        ax.set_ylabel('Excess Return NAV', fontsize=16)
        ax.set_xlabel('Date', fontsize=14)
        ax.legend(fontsize=14)
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/Aggressive_Excess_Return.png", dpi=300, bbox_inches='tight')
        plt.close()

        print("Aggressive visualizations created successfully!")
        return self

    def export_aggressive_results(self):
        """导出激进策略结果"""
        print("Exporting aggressive results...")

        excel_path = f"{self.output_dir}/Aggressive_Bond_Timing_Results.xlsx"

        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # Performance metrics
            metrics_df = pd.DataFrame(list(self.performance_metrics.items()),
                                    columns=['Metric', 'Value'])
            metrics_df.to_excel(writer, sheet_name='Performance_Metrics', index=False)

            # NAV data
            nav_df = self.backtest_results[['strategy_nav', 'benchmark_nav', 'excess_nav']].copy()
            nav_df.columns = ['Strategy_NAV', 'Benchmark_NAV', 'Excess_NAV']
            nav_df.to_excel(writer, sheet_name='NAV_Data')

            # Trading signals
            signals_df = self.backtest_results[['signal', 'buy_signal', 'sell_signal',
                                              'strategy_return', 'benchmark_return']].copy()
            signals_df.columns = ['Position_Signal', 'Buy_Signal', 'Sell_Signal', 'Strategy_Return', 'Benchmark_Return']
            signals_df.to_excel(writer, sheet_name='Trading_Signals')

        print(f"Aggressive results exported to: {excel_path}")
        return self

if __name__ == "__main__":
    print("=== Aggressive Bond Timing Analysis ===")

    # Initialize analysis
    analyzer = BondTimingAggressive("/Users/<USER>/Desktop/中债财富指数择时.xlsx")

    try:
        # Execute aggressive analysis pipeline
        analyzer.load_data()
        analyzer.create_aggressive_features()
        analyzer.select_best_features()
        analyzer.build_aggressive_models()
        analyzer.generate_ultra_aggressive_signals()
        analyzer.backtest_ultra_aggressive()
        analyzer.calculate_aggressive_metrics()
        analyzer.create_aggressive_visualizations()
        analyzer.export_aggressive_results()

        print("\n=== Aggressive Analysis Complete ===")
        print(f"All results saved to: {analyzer.output_dir}")

        # Print key results
        if hasattr(analyzer, 'performance_metrics'):
            strategy_return = analyzer.performance_metrics['Strategy_Annual_Return'] * 100
            benchmark_return = analyzer.performance_metrics['Benchmark_Annual_Return'] * 100
            strategy_dd = analyzer.performance_metrics['Strategy_Max_DD'] * 100
            benchmark_dd = analyzer.performance_metrics['Benchmark_Max_DD'] * 100
            win_rate = analyzer.performance_metrics['Active_Win_Rate'] * 100

            print(f"\nKey Results:")
            print(f"Strategy Annual Return: {strategy_return:.2f}%")
            print(f"Benchmark Annual Return: {benchmark_return:.2f}%")
            print(f"Strategy Max Drawdown: {strategy_dd:.2f}%")
            print(f"Benchmark Max Drawdown: {benchmark_dd:.2f}%")
            print(f"Active Win Rate: {win_rate:.2f}%")

            if strategy_return > benchmark_return:
                print("✅ AGGRESSIVE STRATEGY OUTPERFORMED BENCHMARK!")
            else:
                print("❌ Strategy still underperformed benchmark")

    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()