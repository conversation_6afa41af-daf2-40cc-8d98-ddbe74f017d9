#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债策略对比分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

def analyze_strategy_results():
    """分析策略结果"""
    print("=== 可转债量化策略对比分析 ===\n")
    
    # 策略结果汇总
    strategies = {
        '原始策略': {
            '年化收益率': 0.0031,  # 0.31%
            '胜率': 0.4654,        # 46.54%
            '最大回撤': 0.4120,    # 41.20%
            '夏普比率': 0.01,
            '年化波动率': 0.3602
        },
        '激进策略': {
            '年化收益率': 0.1178,  # 11.78%
            '胜率': 0.5207,        # 52.07%
            '最大回撤': 0.2380,    # 23.80%
            '夏普比率': 0.76,
            '年化波动率': 0.1549
        },
        '超级激进策略': {
            '年化收益率': -0.0027, # -0.27%
            '胜率': 0.4931,        # 49.31%
            '最大回撤': 0.3578,    # 35.78%
            '夏普比率': -0.02,
            '年化波动率': 0.1555
        }
    }
    
    # 目标指标
    targets = {
        '年化收益率': 0.25,    # >25%
        '胜率': 0.60,          # >60%
        '最大回撤': 0.15       # <15%
    }
    
    print("📊 策略业绩对比表")
    print("=" * 80)
    print(f"{'指标':<15} {'原始策略':<12} {'激进策略':<12} {'超级激进':<12} {'目标要求':<12}")
    print("-" * 80)
    
    for metric in ['年化收益率', '胜率', '最大回撤', '夏普比率', '年化波动率']:
        row = f"{metric:<15}"
        for strategy in ['原始策略', '激进策略', '超级激进策略']:
            value = strategies[strategy][metric]
            if metric in ['年化收益率', '胜率', '最大回撤', '年化波动率']:
                row += f"{value:>10.2%}  "
            else:
                row += f"{value:>10.2f}  "
        
        # 添加目标要求
        if metric in targets:
            target = targets[metric]
            row += f"{target:>10.2%}"
        else:
            row += f"{'--':>10}"
        
        print(row)
    
    print("\n🎯 目标达成情况分析")
    print("=" * 50)
    
    for strategy_name, metrics in strategies.items():
        print(f"\n{strategy_name}:")
        
        # 年化收益率
        annual_return = metrics['年化收益率']
        if annual_return >= targets['年化收益率']:
            print(f"  ✅ 年化收益率: {annual_return:.2%} (达标)")
        else:
            print(f"  ❌ 年化收益率: {annual_return:.2%} (未达标，差距{targets['年化收益率']-annual_return:.2%})")
        
        # 胜率
        win_rate = metrics['胜率']
        if win_rate >= targets['胜率']:
            print(f"  ✅ 胜率: {win_rate:.2%} (达标)")
        else:
            print(f"  ❌ 胜率: {win_rate:.2%} (未达标，差距{targets['胜率']-win_rate:.2%})")
        
        # 最大回撤
        max_drawdown = metrics['最大回撤']
        if max_drawdown <= targets['最大回撤']:
            print(f"  ✅ 最大回撤: {max_drawdown:.2%} (达标)")
        else:
            print(f"  ❌ 最大回撤: {max_drawdown:.2%} (未达标，超出{max_drawdown-targets['最大回撤']:.2%})")
    
    print("\n📈 策略优化建议")
    print("=" * 50)
    
    print("\n1. 激进策略表现最佳：")
    print("   - 年化收益率11.78%，虽未达25%目标但显著改善")
    print("   - 胜率52.07%，接近60%目标")
    print("   - 最大回撤23.80%，控制在合理范围")
    print("   - 夏普比率0.76，风险调整后收益良好")
    
    print("\n2. 进一步优化方向：")
    print("   - 🎯 胜率提升：当前52.07% → 目标60%+")
    print("     * 加强反转因子权重")
    print("     * 优化止损止盈策略")
    print("     * 增加市场情绪因子")
    
    print("   - 📈 收益率提升：当前11.78% → 目标25%+")
    print("     * 适度增加仓位集中度")
    print("     * 优化择时信号")
    print("     * 加入宏观经济因子")
    
    print("   - 🛡️ 风险控制：当前回撤23.80% → 目标15%")
    print("     * 动态仓位管理")
    print("     * 市场状态识别")
    print("     * 分散化优化")
    
    print("\n3. 推荐策略配置：")
    print("   - 基础策略：激进策略作为主体")
    print("   - 胜率优化：加强反转因子和估值因子")
    print("   - 风险管理：动态止损和仓位控制")
    print("   - 择券条件：适度放宽以扩大股票池")
    
    # 生成对比图表
    generate_comparison_charts(strategies, targets)
    
    print(f"\n📁 分析报告已保存到桌面")

def generate_comparison_charts(strategies, targets):
    """生成对比图表"""
    
    # 创建输出目录
    output_dir = "/Users/<USER>/Desktop/策略对比分析"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 关键指标对比图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    strategy_names = list(strategies.keys())
    
    # 年化收益率对比
    annual_returns = [strategies[s]['年化收益率'] for s in strategy_names]
    bars1 = ax1.bar(strategy_names, annual_returns, color=['red', 'green', 'blue'], alpha=0.7)
    ax1.axhline(y=targets['年化收益率'], color='orange', linestyle='--', label='Target: 25%')
    ax1.set_title('Annual Return Comparison', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Annual Return')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars1, annual_returns):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.1%}', ha='center', va='bottom')
    
    # 胜率对比
    win_rates = [strategies[s]['胜率'] for s in strategy_names]
    bars2 = ax2.bar(strategy_names, win_rates, color=['red', 'green', 'blue'], alpha=0.7)
    ax2.axhline(y=targets['胜率'], color='orange', linestyle='--', label='Target: 60%')
    ax2.set_title('Win Rate Comparison', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Win Rate')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    for bar, value in zip(bars2, win_rates):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.1%}', ha='center', va='bottom')
    
    # 最大回撤对比
    max_drawdowns = [strategies[s]['最大回撤'] for s in strategy_names]
    bars3 = ax3.bar(strategy_names, max_drawdowns, color=['red', 'green', 'blue'], alpha=0.7)
    ax3.axhline(y=targets['最大回撤'], color='orange', linestyle='--', label='Target: <15%')
    ax3.set_title('Maximum Drawdown Comparison', fontsize=14, fontweight='bold')
    ax3.set_ylabel('Maximum Drawdown')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    for bar, value in zip(bars3, max_drawdowns):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.1%}', ha='center', va='bottom')
    
    # 夏普比率对比
    sharpe_ratios = [strategies[s]['夏普比率'] for s in strategy_names]
    bars4 = ax4.bar(strategy_names, sharpe_ratios, color=['red', 'green', 'blue'], alpha=0.7)
    ax4.set_title('Sharpe Ratio Comparison', fontsize=14, fontweight='bold')
    ax4.set_ylabel('Sharpe Ratio')
    ax4.grid(True, alpha=0.3)
    
    for bar, value in zip(bars4, sharpe_ratios):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                f'{value:.2f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(f"{output_dir}/策略对比分析图.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 雷达图
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # 标准化指标（0-1范围）
    metrics = ['Annual Return', 'Win Rate', 'Sharpe Ratio', 'Risk Control', 'Volatility Control']
    
    for i, (strategy_name, strategy_data) in enumerate(strategies.items()):
        if strategy_name == '激进策略':  # 只显示最佳策略
            values = [
                min(strategy_data['年化收益率'] / 0.3, 1),  # 年化收益率
                strategy_data['胜率'],  # 胜率
                min(max(strategy_data['夏普比率'] / 2, 0), 1),  # 夏普比率
                max(1 - strategy_data['最大回撤'] / 0.5, 0),  # 风险控制
                max(1 - strategy_data['年化波动率'] / 0.4, 0)   # 波动率控制
            ]
            
            angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]
            
            ax.plot(angles, values, 'o-', linewidth=2, label=strategy_name, color='green')
            ax.fill(angles, values, alpha=0.25, color='green')
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics)
    ax.set_ylim(0, 1)
    ax.set_title('Strategy Performance Radar Chart\n(Aggressive Strategy)', 
                 fontsize=16, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax.grid(True)
    
    plt.savefig(f"{output_dir}/策略雷达图.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    print("图表生成完成")

if __name__ == "__main__":
    analyze_strategy_results()
