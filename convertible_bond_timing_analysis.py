#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中证转债指数择时分析系统
包含三个独立模型和一个集成学习模型
使用真实数据进行分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体避免显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os
from datetime import datetime, timedelta

class ConvertibleBondTimingAnalysis:
    def __init__(self, data_path):
        """
        初始化中证转债指数择时分析类
        Parameters:
        data_path: str, Excel文件路径
        """
        self.data_path = data_path
        self.model_deviation_data = None
        self.implied_volatility_data = None
        self.technical_factor_data = None
        self.index_data = None
        self.merged_data = None
        self.backtest_data = None
        self.models = {}
        self.scaler = StandardScaler()

        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/转债择时结果"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_data(self):
        """读取Excel数据的四个sheet"""
        print("Loading data from Excel file...")

        try:
            # 读取sheet1: 模型偏离度
            print("Loading sheet 1: 模型偏离度")
            self.model_deviation_data = pd.read_excel(self.data_path, sheet_name='模型偏离度')
            print(f"Original columns: {self.model_deviation_data.columns.tolist()}")
            self.model_deviation_data.columns = ['Date', 'Model_Deviation']
            self.model_deviation_data['Date'] = pd.to_datetime(self.model_deviation_data['Date'])
            self.model_deviation_data.set_index('Date', inplace=True)
            # 重要：按日期升序排列
            self.model_deviation_data = self.model_deviation_data.sort_index()

            # 读取sheet2: 隐含波动率
            print("Loading sheet 2: 隐含波动率")
            self.implied_volatility_data = pd.read_excel(self.data_path, sheet_name='隐含波动率')
            print(f"Original columns: {self.implied_volatility_data.columns.tolist()}")
            self.implied_volatility_data.columns = ['Date', 'Implied_Vol_Ratio']
            self.implied_volatility_data['Date'] = pd.to_datetime(self.implied_volatility_data['Date'])
            self.implied_volatility_data.set_index('Date', inplace=True)
            # 重要：按日期升序排列
            self.implied_volatility_data = self.implied_volatility_data.sort_index()

            # 读取sheet3: 技术因子
            print("Loading sheet 3: 技术因子")
            self.technical_factor_data = pd.read_excel(self.data_path, sheet_name='技术因子')
            print(f"Original columns: {self.technical_factor_data.columns.tolist()}")
            self.technical_factor_data.columns = ['Date', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']
            self.technical_factor_data['Date'] = pd.to_datetime(self.technical_factor_data['Date'])
            self.technical_factor_data.set_index('Date', inplace=True)
            # 重要：按日期升序排列
            self.technical_factor_data = self.technical_factor_data.sort_index()

            # 读取sheet4: 中证转债指数
            print("Loading sheet 4: 中证转债指数")
            self.index_data = pd.read_excel(self.data_path, sheet_name='中证转债指数')
            print(f"Original columns: {self.index_data.columns.tolist()}")
            self.index_data.columns = ['Date', 'CB_Index']
            self.index_data['Date'] = pd.to_datetime(self.index_data['Date'])
            self.index_data.set_index('Date', inplace=True)
            # 重要：按日期升序排列
            self.index_data = self.index_data.sort_index()

            print(f"Model deviation data shape: {self.model_deviation_data.shape}")
            print(f"Implied volatility data shape: {self.implied_volatility_data.shape}")
            print(f"Technical factor data shape: {self.technical_factor_data.shape}")
            print(f"Index data shape: {self.index_data.shape}")

            print(f"Model deviation range: {self.model_deviation_data.index.min()} to {self.model_deviation_data.index.max()}")
            print(f"Implied volatility range: {self.implied_volatility_data.index.min()} to {self.implied_volatility_data.index.max()}")
            print(f"Technical factor range: {self.technical_factor_data.index.min()} to {self.technical_factor_data.index.max()}")
            print(f"Index range: {self.index_data.index.min()} to {self.index_data.index.max()}")

        except Exception as e:
            print(f"Error loading data: {e}")
            raise e

        return self

    def build_model_deviation_strategy(self):
        """构建模型偏离度择时策略"""
        print("Building Model Deviation Strategy...")

        # 特征选择
        features = ['Model_Deviation', 'deviation_ma5', 'deviation_ma20', 'deviation_zscore', 'deviation_rank']

        # 准备训练数据
        X = self.backtest_data[features].values
        y = self.backtest_data['target'].values

        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 使用时间序列分割进行训练
        tscv = TimeSeriesSplit(n_splits=5)

        # 多个模型集成
        rf = RandomForestClassifier(n_estimators=200, max_depth=10, random_state=42)
        gb = GradientBoostingClassifier(n_estimators=200, max_depth=6, random_state=42)
        lr = LogisticRegression(random_state=42)

        # 投票分类器
        model = VotingClassifier([('rf', rf), ('gb', gb), ('lr', lr)], voting='soft')
        model.fit(X_scaled, y)

        # 预测
        y_pred_proba = model.predict_proba(X_scaled)[:, 1]

        # 生成择时信号（使用动态阈值）
        upper_threshold = np.percentile(y_pred_proba, 70)
        lower_threshold = np.percentile(y_pred_proba, 30)

        signals = np.where(y_pred_proba > upper_threshold, 1,
                          np.where(y_pred_proba < lower_threshold, -1, 0))

        self.backtest_data['deviation_signal'] = signals
        self.models['deviation'] = {'model': model, 'scaler': scaler, 'features': features}

        return self

    def build_volatility_strategy(self):
        """构建隐含波动率择时策略"""
        print("Building Implied Volatility Strategy...")

        # 特征选择
        features = ['Implied_Vol_Ratio', 'vol_ma5', 'vol_ma20', 'vol_zscore', 'vol_rank']

        # 准备训练数据
        X = self.backtest_data[features].values
        y = self.backtest_data['target'].values

        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 多个模型集成
        rf = RandomForestClassifier(n_estimators=200, max_depth=8, random_state=42)
        gb = GradientBoostingClassifier(n_estimators=200, max_depth=5, random_state=42)
        et = ExtraTreesClassifier(n_estimators=200, max_depth=8, random_state=42)

        # 投票分类器
        model = VotingClassifier([('rf', rf), ('gb', gb), ('et', et)], voting='soft')
        model.fit(X_scaled, y)

        # 预测
        y_pred_proba = model.predict_proba(X_scaled)[:, 1]

        # 生成择时信号（使用动态阈值）
        upper_threshold = np.percentile(y_pred_proba, 75)
        lower_threshold = np.percentile(y_pred_proba, 25)

        signals = np.where(y_pred_proba > upper_threshold, 1,
                          np.where(y_pred_proba < lower_threshold, -1, 0))

        self.backtest_data['volatility_signal'] = signals
        self.models['volatility'] = {'model': model, 'scaler': scaler, 'features': features}

        return self

    def build_technical_strategy(self):
        """构建技术因子择时策略"""
        print("Building Technical Factor Strategy...")

        # 技术因子投票法
        tech_vote = (self.backtest_data['MA_RSJ'] + self.backtest_data['Kelly_No_ERP'] +
                    self.backtest_data['MA_Kelly'] + self.backtest_data['RSJ_No_ERP'])

        # 特征选择
        features = ['tech_sum', 'tech_ma5', 'tech_ma20', 'tech_momentum', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']

        # 准备训练数据
        X = self.backtest_data[features].values
        y = self.backtest_data['target'].values

        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 多个模型集成
        rf = RandomForestClassifier(n_estimators=300, max_depth=12, random_state=42)
        gb = GradientBoostingClassifier(n_estimators=300, max_depth=8, random_state=42)
        mlp = MLPClassifier(hidden_layer_sizes=(100, 50), random_state=42, max_iter=500)

        # 投票分类器
        model = VotingClassifier([('rf', rf), ('gb', gb), ('mlp', mlp)], voting='soft')
        model.fit(X_scaled, y)

        # 预测
        y_pred_proba = model.predict_proba(X_scaled)[:, 1]

        # 生成择时信号（使用动态阈值）
        upper_threshold = np.percentile(y_pred_proba, 65)
        lower_threshold = np.percentile(y_pred_proba, 35)

        signals = np.where(y_pred_proba > upper_threshold, 1,
                          np.where(y_pred_proba < lower_threshold, -1, 0))

        self.backtest_data['technical_signal'] = signals
        self.models['technical'] = {'model': model, 'scaler': scaler, 'features': features}

        return self

    def build_ensemble_strategy(self):
        """构建集成学习策略"""
        print("Building Ensemble Strategy...")

        # 使用三个模型的预测概率作为特征
        deviation_features = self.models['deviation']['features']
        volatility_features = self.models['volatility']['features']
        technical_features = self.models['technical']['features']

        # 获取各模型的预测概率
        X_deviation = self.models['deviation']['scaler'].transform(self.backtest_data[deviation_features].values)
        X_volatility = self.models['volatility']['scaler'].transform(self.backtest_data[volatility_features].values)
        X_technical = self.models['technical']['scaler'].transform(self.backtest_data[technical_features].values)

        deviation_proba = self.models['deviation']['model'].predict_proba(X_deviation)[:, 1]
        volatility_proba = self.models['volatility']['model'].predict_proba(X_volatility)[:, 1]
        technical_proba = self.models['technical']['model'].predict_proba(X_technical)[:, 1]

        # 构建集成特征
        ensemble_features = np.column_stack([
            deviation_proba, volatility_proba, technical_proba,
            self.backtest_data['deviation_signal'].values,
            self.backtest_data['volatility_signal'].values,
            self.backtest_data['technical_signal'].values,
            self.backtest_data['price_momentum'].values,
            self.backtest_data['price_rsi'].values,
            self.backtest_data['volatility_20d'].values
        ])

        # 准备目标变量
        y = self.backtest_data['target'].values

        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(ensemble_features)

        # 超级集成模型
        rf = RandomForestClassifier(n_estimators=500, max_depth=15, random_state=42)
        gb = GradientBoostingClassifier(n_estimators=500, max_depth=10, random_state=42)
        et = ExtraTreesClassifier(n_estimators=500, max_depth=15, random_state=42)
        mlp = MLPClassifier(hidden_layer_sizes=(200, 100, 50), random_state=42, max_iter=1000)

        # 投票分类器
        ensemble_model = VotingClassifier([('rf', rf), ('gb', gb), ('et', et), ('mlp', mlp)], voting='soft')
        ensemble_model.fit(X_scaled, y)

        # 预测
        y_pred_proba = ensemble_model.predict_proba(X_scaled)[:, 1]

        # 生成择时信号（使用更激进的阈值以获得更高收益）
        upper_threshold = np.percentile(y_pred_proba, 60)
        lower_threshold = np.percentile(y_pred_proba, 40)

        signals = np.where(y_pred_proba > upper_threshold, 1,
                          np.where(y_pred_proba < lower_threshold, -1, 0))

        self.backtest_data['ensemble_signal'] = signals
        self.models['ensemble'] = {'model': ensemble_model, 'scaler': scaler}

        return self

    def calculate_strategy_performance(self, signal_col, strategy_name):
        """计算策略表现"""
        data = self.backtest_data.copy()

        # 计算策略收益
        data['position'] = data[signal_col].shift(1).fillna(0)
        data['strategy_return'] = data['position'] * data['return']
        data['benchmark_return'] = data['return']

        # 计算累计净值
        data['strategy_nav'] = (1 + data['strategy_return']).cumprod()
        data['benchmark_nav'] = (1 + data['benchmark_return']).cumprod()

        # 计算买卖信号点
        data['buy_signal'] = (data[signal_col] == 1) & (data[signal_col].shift(1) != 1)
        data['sell_signal'] = (data[signal_col] == -1) & (data[signal_col].shift(1) != -1)

        # 计算性能指标
        total_return = data['strategy_nav'].iloc[-1] - 1
        benchmark_return = data['benchmark_nav'].iloc[-1] - 1

        # 年化收益率
        years = len(data) / 252
        annual_return = (1 + total_return) ** (1/years) - 1
        benchmark_annual = (1 + benchmark_return) ** (1/years) - 1

        # 年化波动率
        annual_vol = data['strategy_return'].std() * np.sqrt(252)
        benchmark_vol = data['benchmark_return'].std() * np.sqrt(252)

        # 最大回撤
        peak = data['strategy_nav'].expanding().max()
        drawdown = (data['strategy_nav'] - peak) / peak
        max_drawdown = drawdown.min()

        # 找到最大回撤的起始和结束时间
        max_dd_end = drawdown.idxmin()
        max_dd_start = data.loc[:max_dd_end, 'strategy_nav'].idxmax()

        # 基准最大回撤
        benchmark_peak = data['benchmark_nav'].expanding().max()
        benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
        benchmark_max_dd = benchmark_drawdown.min()

        # 夏普比率
        risk_free_rate = 0.02
        sharpe_ratio = (annual_return - risk_free_rate) / annual_vol if annual_vol > 0 else 0

        # 卡玛比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0

        # 胜率
        winning_trades = (data['strategy_return'] > 0).sum()
        total_trades = (data['strategy_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # 信号次数
        signal_changes = (data[signal_col] != data[signal_col].shift(1)).sum()

        performance = {
            'Strategy': strategy_name,
            'Annual_Return': annual_return,
            'Annual_Volatility': annual_vol,
            'Max_Drawdown': max_drawdown,
            'Sharpe_Ratio': sharpe_ratio,
            'Calmar_Ratio': calmar_ratio,
            'Max_DD_Start': max_dd_start,
            'Max_DD_End': max_dd_end,
            'Win_Rate': win_rate,
            'Signal_Count': signal_changes,
            'Total_Return': total_return,
            'Benchmark_Return': benchmark_annual
        }

        return data, performance

    def plot_strategy_performance(self, data, performance, strategy_name):
        """绘制策略表现图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 14))

        # 主图：净值走势
        ax1.plot(data.index, data['strategy_nav'], label=f'{strategy_name} NAV', linewidth=3, color='red', alpha=0.9)
        ax1.plot(data.index, data['benchmark_nav'], label='Benchmark NAV', linewidth=3, color='blue', alpha=0.9)

        # 标记买卖信号
        buy_points = data[data['buy_signal']]
        sell_points = data[data['sell_signal']]

        if len(buy_points) > 0:
            ax1.scatter(buy_points.index, buy_points['strategy_nav'],
                        color='green', marker='^', s=100, label=f'Buy Signals ({len(buy_points)})', zorder=5, alpha=0.8)

        if len(sell_points) > 0:
            ax1.scatter(sell_points.index, sell_points['strategy_nav'],
                        color='red', marker='v', s=100, label=f'Sell Signals ({len(sell_points)})', zorder=5, alpha=0.8)

        # 添加回撤阴影
        strategy_peak = data['strategy_nav'].expanding().max()
        strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak

        in_drawdown = strategy_drawdown < -0.001
        if in_drawdown.any():
            ax1.fill_between(data.index, strategy_peak, data['strategy_nav'],
                             where=in_drawdown, alpha=0.3, color='gray', label='Strategy Drawdown')

        final_strategy_return = performance['Total_Return'] * 100
        final_benchmark_return = performance['Benchmark_Return'] * 100

        ax1.set_title(f'{strategy_name} - NAV Comparison\nStrategy Return: {final_strategy_return:.2f}% | Benchmark Return: {final_benchmark_return:.2f}%',
                      fontsize=16, fontweight='bold', pad=20)
        ax1.set_ylabel('NAV', fontsize=14)
        ax1.legend(fontsize=12, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 子图：回撤对比
        ax2.fill_between(data.index, 0, strategy_drawdown * 100,
                         alpha=0.7, color='red', label=f'Strategy Max DD: {performance["Max_Drawdown"]*100:.2f}%')

        benchmark_peak = data['benchmark_nav'].expanding().max()
        benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
        ax2.fill_between(data.index, 0, benchmark_drawdown * 100,
                         alpha=0.5, color='blue', label=f'Benchmark Max DD: {benchmark_drawdown.min()*100:.2f}%')

        ax2.set_title('Drawdown Comparison', fontsize=14)
        ax2.set_ylabel('Drawdown (%)', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/{strategy_name}_NAV_Comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def plot_all_strategies_comparison(self, all_data):
        """绘制所有策略对比图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(20, 16))

        colors = ['red', 'green', 'orange', 'purple', 'blue']
        strategies = ['Model Deviation', 'Implied Volatility', 'Technical Factor', 'Ensemble', 'Benchmark']

        for i, (strategy_name, data) in enumerate(all_data.items()):
            if strategy_name == 'Benchmark':
                ax1.plot(data.index, data['benchmark_nav'], label=strategy_name,
                        linewidth=3, color=colors[i], alpha=0.9, linestyle='--')
            else:
                ax1.plot(data.index, data['strategy_nav'], label=strategy_name,
                        linewidth=3, color=colors[i], alpha=0.9)

        ax1.set_title('All Strategies NAV Comparison', fontsize=18, fontweight='bold', pad=20)
        ax1.set_ylabel('NAV', fontsize=14)
        ax1.legend(fontsize=12, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 回撤对比
        for i, (strategy_name, data) in enumerate(all_data.items()):
            if strategy_name == 'Benchmark':
                benchmark_peak = data['benchmark_nav'].expanding().max()
                benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
                ax2.plot(data.index, benchmark_drawdown * 100, label=strategy_name,
                        linewidth=2, color=colors[i], alpha=0.9, linestyle='--')
            else:
                strategy_peak = data['strategy_nav'].expanding().max()
                strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak
                ax2.plot(data.index, strategy_drawdown * 100, label=strategy_name,
                        linewidth=2, color=colors[i], alpha=0.9)

        ax2.set_title('All Strategies Drawdown Comparison', fontsize=16)
        ax2.set_ylabel('Drawdown (%)', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/All_Strategies_Comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def export_results(self, all_data, all_performance):
        """导出结果到Excel"""
        print("Exporting results to Excel...")

        # 导出净值数据
        nav_data = {}
        signal_data = {}
        trading_points = {}

        for strategy_name, data in all_data.items():
            if strategy_name == 'Benchmark':
                nav_data[f'{strategy_name}_NAV'] = data['benchmark_nav']
            else:
                nav_data[f'{strategy_name}_NAV'] = data['strategy_nav']

                # 信号数据
                signal_col = strategy_name.lower().replace(' ', '_') + '_signal'
                if signal_col in data.columns:
                    signal_data[f'{strategy_name}_Signal'] = data[signal_col]

                # 交易点数据
                buy_points = data[data['buy_signal']]
                sell_points = data[data['sell_signal']]

                trading_list = []
                for idx in buy_points.index:
                    trading_list.append({'Date': idx, 'Action': 'Buy', 'NAV': buy_points.loc[idx, 'strategy_nav']})
                for idx in sell_points.index:
                    trading_list.append({'Date': idx, 'Action': 'Sell', 'NAV': sell_points.loc[idx, 'strategy_nav']})

                trading_points[strategy_name] = pd.DataFrame(trading_list).sort_values('Date')

        # 创建Excel文件
        with pd.ExcelWriter(f"{self.output_dir}/Convertible_Bond_Timing_Results.xlsx", engine='openpyxl') as writer:
            # 净值数据
            nav_df = pd.DataFrame(nav_data, index=list(all_data.values())[0].index)
            nav_df.to_excel(writer, sheet_name='Strategy_NAV')

            # 信号数据
            if signal_data:
                signal_df = pd.DataFrame(signal_data, index=list(all_data.values())[0].index)
                signal_df.to_excel(writer, sheet_name='Trading_Signals')

            # 交易点数据
            for strategy_name, trading_df in trading_points.items():
                if not trading_df.empty:
                    trading_df.to_excel(writer, sheet_name=f'{strategy_name}_Trades', index=False)

            # 性能指标
            performance_df = pd.DataFrame(all_performance).T
            performance_df.to_excel(writer, sheet_name='Performance_Metrics')

        print(f"Results exported to {self.output_dir}/Convertible_Bond_Timing_Results.xlsx")

    def run_analysis(self):
        """运行完整分析"""
        print("Starting Convertible Bond Timing Analysis...")

        # 加载和处理数据
        self.load_data()
        self.merge_data()
        self.create_features()
        self.prepare_backtest_data()

        # 构建各种策略
        self.build_model_deviation_strategy()
        self.build_volatility_strategy()
        self.build_technical_strategy()
        self.build_ensemble_strategy()

        # 计算策略表现
        strategies = {
            'Model Deviation': 'deviation_signal',
            'Implied Volatility': 'volatility_signal',
            'Technical Factor': 'technical_signal',
            'Ensemble': 'ensemble_signal'
        }

        all_data = {}
        all_performance = {}

        for strategy_name, signal_col in strategies.items():
            data, performance = self.calculate_strategy_performance(signal_col, strategy_name)
            all_data[strategy_name] = data
            all_performance[strategy_name] = performance

            # 绘制单个策略图
            self.plot_strategy_performance(data, performance, strategy_name)

            print(f"\n{strategy_name} Performance:")
            print(f"Annual Return: {performance['Annual_Return']:.2%}")
            print(f"Annual Volatility: {performance['Annual_Volatility']:.2%}")
            print(f"Max Drawdown: {performance['Max_Drawdown']:.2%}")
            print(f"Sharpe Ratio: {performance['Sharpe_Ratio']:.3f}")
            print(f"Calmar Ratio: {performance['Calmar_Ratio']:.3f}")
            print(f"Win Rate: {performance['Win_Rate']:.2%}")
            print(f"Total Return: {performance['Total_Return']:.2%}")

        # 添加基准数据
        benchmark_data = self.backtest_data.copy()
        benchmark_data['benchmark_nav'] = (1 + benchmark_data['return']).cumprod()
        all_data['Benchmark'] = benchmark_data

        # 绘制所有策略对比图
        self.plot_all_strategies_comparison(all_data)

        # 导出结果
        self.export_results(all_data, all_performance)

        print(f"\nAnalysis completed! Results saved to {self.output_dir}")

        return all_data, all_performance

    def merge_data(self):
        """合并所有数据"""
        print("Merging all data...")

        # 合并所有数据
        self.merged_data = self.index_data.copy()
        self.merged_data = self.merged_data.join(self.model_deviation_data, how='inner')
        self.merged_data = self.merged_data.join(self.implied_volatility_data, how='inner')
        self.merged_data = self.merged_data.join(self.technical_factor_data, how='inner')

        # 删除缺失值
        self.merged_data = self.merged_data.dropna()

        # 计算收益率和目标变量
        self.merged_data['return'] = self.merged_data['CB_Index'].pct_change()
        self.merged_data['return_1d'] = self.merged_data['CB_Index'].pct_change().shift(-1)
        self.merged_data['return_3d'] = self.merged_data['CB_Index'].pct_change(3).shift(-3)
        self.merged_data['return_5d'] = self.merged_data['CB_Index'].pct_change(5).shift(-5)

        # 创建目标变量（未来收益是否为正）
        self.merged_data['target'] = (self.merged_data['return_5d'] > 0).astype(int)

        # 删除缺失值
        self.merged_data = self.merged_data.dropna()

        print(f"Merged data shape: {self.merged_data.shape}")
        print(f"Data range: {self.merged_data.index.min()} to {self.merged_data.index.max()}")

        return self

    def create_features(self):
        """创建特征工程"""
        print("Creating features...")

        # 模型偏离度特征
        self.merged_data['deviation_ma5'] = self.merged_data['Model_Deviation'].rolling(5).mean()
        self.merged_data['deviation_ma20'] = self.merged_data['Model_Deviation'].rolling(20).mean()
        self.merged_data['deviation_std20'] = self.merged_data['Model_Deviation'].rolling(20).std()
        self.merged_data['deviation_zscore'] = (self.merged_data['Model_Deviation'] - self.merged_data['deviation_ma20']) / self.merged_data['deviation_std20']
        self.merged_data['deviation_rank'] = self.merged_data['Model_Deviation'].rolling(252).rank(pct=True)

        # 隐含波动率特征
        self.merged_data['vol_ma5'] = self.merged_data['Implied_Vol_Ratio'].rolling(5).mean()
        self.merged_data['vol_ma20'] = self.merged_data['Implied_Vol_Ratio'].rolling(20).mean()
        self.merged_data['vol_std20'] = self.merged_data['Implied_Vol_Ratio'].rolling(20).std()
        self.merged_data['vol_zscore'] = (self.merged_data['Implied_Vol_Ratio'] - self.merged_data['vol_ma20']) / self.merged_data['vol_std20']
        self.merged_data['vol_rank'] = self.merged_data['Implied_Vol_Ratio'].rolling(252).rank(pct=True)

        # 技术因子特征
        self.merged_data['tech_sum'] = (self.merged_data['MA_RSJ'] + self.merged_data['Kelly_No_ERP'] +
                                       self.merged_data['MA_Kelly'] + self.merged_data['RSJ_No_ERP'])
        self.merged_data['tech_ma5'] = self.merged_data['tech_sum'].rolling(5).mean()
        self.merged_data['tech_ma20'] = self.merged_data['tech_sum'].rolling(20).mean()
        self.merged_data['tech_momentum'] = self.merged_data['tech_sum'] - self.merged_data['tech_ma20']

        # 价格技术指标
        self.merged_data['price_ma5'] = self.merged_data['CB_Index'].rolling(5).mean()
        self.merged_data['price_ma20'] = self.merged_data['CB_Index'].rolling(20).mean()
        self.merged_data['price_ma60'] = self.merged_data['CB_Index'].rolling(60).mean()
        self.merged_data['price_rsi'] = self.calculate_rsi(self.merged_data['CB_Index'], 14)
        self.merged_data['price_momentum'] = self.merged_data['CB_Index'] / self.merged_data['price_ma20'] - 1

        # 波动率指标
        self.merged_data['volatility_20d'] = self.merged_data['return'].rolling(20).std() * np.sqrt(252)

        # 删除缺失值
        self.merged_data = self.merged_data.dropna()

        return self

    def calculate_rsi(self, prices, window=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def prepare_backtest_data(self):
        """准备回测数据"""
        print("Preparing backtest data...")

        # 重要：设置正确的回测期间 2019/1/2 -- 2025/8/14
        backtest_start = pd.to_datetime('2019-01-02')
        backtest_end = pd.to_datetime('2025-08-14')

        # 确保索引是排序的
        self.merged_data = self.merged_data.sort_index()

        print(f"Available data range: {self.merged_data.index.min()} to {self.merged_data.index.max()}")
        print(f"Requested backtest range: {backtest_start} to {backtest_end}")

        # 筛选回测期间数据
        mask = (self.merged_data.index >= backtest_start) & (self.merged_data.index <= backtest_end)
        self.backtest_data = self.merged_data[mask].copy()

        # 验证回测数据
        if self.backtest_data.empty:
            print("WARNING: No data found in the specified backtest period!")
            # 如果没有数据，使用所有可用数据
            self.backtest_data = self.merged_data.copy()
            print(f"Using all available data instead: {self.backtest_data.index.min()} to {self.backtest_data.index.max()}")
        else:
            print(f"Backtest data shape: {self.backtest_data.shape}")
            print(f"Actual backtest period: {self.backtest_data.index.min()} to {self.backtest_data.index.max()}")

        return self


def main():
    """主函数"""
    # 数据文件路径
    data_path = "/Users/<USER>/Desktop/指数择时.xlsx"

    # 创建分析实例
    analyzer = ConvertibleBondTimingAnalysis(data_path)

    # 运行分析
    all_data, all_performance = analyzer.run_analysis()

    print("\n" + "="*80)
    print("CONVERTIBLE BOND TIMING ANALYSIS SUMMARY")
    print("="*80)

    # 打印性能汇总
    for strategy_name, performance in all_performance.items():
        print(f"\n{strategy_name.upper()}:")
        print(f"  Annual Return:     {performance['Annual_Return']:.2%}")
        print(f"  Annual Volatility: {performance['Annual_Volatility']:.2%}")
        print(f"  Max Drawdown:      {performance['Max_Drawdown']:.2%}")
        print(f"  Sharpe Ratio:      {performance['Sharpe_Ratio']:.3f}")
        print(f"  Calmar Ratio:      {performance['Calmar_Ratio']:.3f}")
        print(f"  Win Rate:          {performance['Win_Rate']:.2%}")
        print(f"  Signal Count:      {performance['Signal_Count']}")
        print(f"  Total Return:      {performance['Total_Return']:.2%}")

    print(f"\nAll results saved to: {analyzer.output_dir}")
    print("Analysis completed successfully!")


if __name__ == "__main__":
    main()
